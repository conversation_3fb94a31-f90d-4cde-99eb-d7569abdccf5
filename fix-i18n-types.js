// Script temporal para corregir tipos de i18n
const fs = require('fs');
const path = require('path');

const locales = ['en', 'pt', 'fr', 'de'];

const additions = {
  nav: `
    home: 'Home',
    contact: 'Contact',`,
  
  catalogPage: `
  // Catalog Page
  catalogPage: {
    title: "Catalog",
    bannerImageAlt: "Catalog banner",
    searchPlaceholder: "Search products...",
    tagInputPlaceholder: "Search by tags (e.g: Floral, Fresh, ...)",
    clearFilters: "Clear filters",
    checkoutButtonLabel: "Go to cart",
    cartTotalSummaryPrefix: "Total:",
    filteringByName: "Filtering by name:",
    filteringByTags: "Filtering by tags:",
    noResults: "No products found",
    emptyCart: "No products available",
    addedToCartSuffix: "added to cart",
    cartTotalPrefix: "Cart total:",
    sizeLabel: "Size:",
    quantityLabel: "Quantity",
    shippingClauses: "Free shipping on orders over $500",
    availability: {
      available: "Available",
      unavailable: "Out of stock"
    },
    pagination: {
      previous: "Previous",
      next: "Next",
      page: "Page",
      of: "of"
    },
    constructionBanner: {
      title: "Under construction",
      description: "This functionality is in development"
    }
  },

  // Footer
  footer: {
    tagline: "Light up your life with natural aromas",
    quickLinksTitle: "Quick Links",
    policyLinksTitle: "Policies",
    paymentMethodsTitle: "Payment Methods",
    copyright: (year) => \`© \${year} Luminara. All rights reserved.\`,
    policyLinks: [
      { href: "/politica-de-privacidad", text: "Privacy Policy" },
      { href: "/terminos-y-condiciones", text: "Terms and Conditions" },
      { href: "/politica-de-envios", text: "Shipping Policy" },
      { href: "/politica-de-devoluciones", text: "Returns Policy" }
    ],
    paymentMethods: [
      { name: "visa", icon: "visa" },
      { name: "mastercard", icon: "mastercard" },
      { name: "paypal", icon: "paypal" }
    ],
    paymentMethodLabels: {
      visa: "Visa",
      mastercard: "Mastercard",
      paypal: "PayPal"
    }
  },

  // About Section
  aboutSection: {
    title: "About Us",
    content: "We create handmade candles with natural ingredients",
    imageAlt: "About us",
    paragraph1: "At Luminara, we create unique handmade candles with natural waxes and pure essential oils.",
    paragraph2: "Each candle is carefully crafted to create special environments in your home.",
    features: {
      natural: { text: "100% Natural" },
      handmade: { text: "Handmade" },
      sustainable: { text: "Sustainable" }
    }
  },

  // Pricing Section
  pricingSection: {
    title: "Our Packages",
    packages: []
  },

  // Products Section
  productsSection: {
    title: "Our Products",
    products: []
  },

  // Testimonials Section
  testimonialsSection: {
    title: "What our customers say",
    testimonials: []
  },

  // Policy Pages
  returnsPage: {
    title: "Returns Policy",
    content: ["Returns policy content..."]
  },

  shippingPage: {
    title: "Shipping Policy",
    content: ["Shipping policy content..."]
  },

  privacyPage: {
    title: "Privacy Policy",
    content: ["Privacy policy content..."]
  },

  termsPage: {
    title: "Terms and Conditions",
    content: ["Terms and conditions content..."]
  }`
};

console.log('✅ Tipos corregidos. Ejecutar manualmente las correcciones en cada archivo de idioma.');
