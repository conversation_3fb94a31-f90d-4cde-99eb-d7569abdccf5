
import type { NextConfig } from 'next';

// Configuración para Next.js con Netlify Forms
const nextConfig: NextConfig = {
  // Configuración básica de React
  reactStrictMode: true,
  
  // NO usar 'output: export' cuando se usa Netlify Forms
  
  // Image optimization configuration
  images: {
    unoptimized: true, // Required for Netlify
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'randomuser.me',
        port: '',
        pathname: '/api/portraits/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'upload.wikimedia.org',
        port: '',
        pathname: '/**',
      }
    ],
  },
  
  // Do NOT use output: 'export' or output: 'standalone'
  // Let Netlify plugin handle the output setting
  
  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
