// src/types/product.ts
export interface Product {
  id: string;
  imageSrc: string;
  imageAlt: string;
  name: string;
  description: string; // Short description / Punch line
  longDescription: string; // Detailed description
  price: string;
  availability: boolean;
  stock: number; // Current stock quantity
  reorderPoint: number; // Reorder point for inventory management
  size: 'chica' | 'mediana' | 'grande' | string;
  tags: string[];
  gallery: string[]; // Array of image URLs for carousel
  imageHint?: string;
}
