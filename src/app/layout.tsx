// src/app/layout.tsx
'use client';

import { Inria_Serif, Open_Sans } from 'next/font/google';
import './globals.css';
import { Toaster } from '@/components/ui/toaster';
import { appStrings } from '@/helpers/strings';
import { ReduxProvider } from '@/components/layout/redux-provider';
import { PageLoader } from '@/components/layout/page-loader';
import { I18nDataProvider } from '@/contexts/I18nDataContext';
import { GlobalI18nOverlay } from '@/components/layout/GlobalI18nOverlay';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useDispatch } from 'react-redux';
import type { AppDispatch } from '@/store/store';
import { setPageLoading } from '@/store/slices/ui-slice';

const inriaSerif = Inria_Serif({
  subsets: ['latin'],
  variable: '--font-inria-serif',
  weight: ['400', '700'],
});

const openSans = Open_Sans({
  subsets: ['latin'],
  variable: '--font-open-sans',
  weight: ['300', '400', '600'],
});

// Helper component to manage page loading logic within ReduxProvider context
function PageLoadingManager({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();
  const pathname = usePathname();
  const previousPathname = useRef(pathname);

  // Duration for the loader to be visible. Adjust as needed.
  const LOADER_VISIBLE_DURATION = 500; // ms

  useEffect(() => {
    if (previousPathname.current !== pathname) {
      dispatch(setPageLoading(true));
      previousPathname.current = pathname;

      const timer = setTimeout(() => {
        dispatch(setPageLoading(false));
      }, LOADER_VISIBLE_DURATION);

      return () => clearTimeout(timer);
    }
  }, [pathname, dispatch]);

  return <>{children}</>;
}


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="es">
      <head>
        <title>{appStrings.site.meta.title}</title>
        <meta name="description" content={appStrings.site.meta.description} />
      </head>
      <body className={`${openSans.variable} ${inriaSerif.variable} antialiased`}>
        <ReduxProvider>
          <I18nDataProvider>
            <PageLoadingManager>
              {children}
            </PageLoadingManager>
            <GlobalI18nOverlay />
            <Toaster />
          </I18nDataProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
