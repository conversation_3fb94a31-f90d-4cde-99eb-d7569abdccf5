@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 28 56% 94%; /* Pale Amber #FAF0E6 */
    --foreground: 220 13% 20%; /* Dark Gray */

    --card: 0 0% 100%; /* White */
    --card-foreground: 220 13% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 20%;

    --primary: 42 100% 50%; /* Amber #FFB300 */
    --primary-foreground: 220 13% 10%; /* Darkest Gray for text on primary */

    --secondary: 42 100% 75%; /* Darker Lighter Amber */
    --secondary-foreground: 42 100% 25%; /* Darker Amber for text on new secondary */
    
    --muted: 220 13% 85%; /* Lighter Gray for muted elements */
    --muted-foreground: 220 13% 45%; /* Medium Gray for muted text */

    --accent: 51 100% 40%; /* Darker Golden Yellow */
    --accent-foreground: 0 0% 100%; /* White for text on new darker accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 42 100% 80%; /* Amber-toned border */
    --input: 42 100% 88%; /* Amber-toned input background */
    --ring: 42 100% 50%; /* Primary color for rings */

    --radius: 0.75rem; /* Slightly more rounded corners for a softer feel */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 220 10% 10%; 
    --foreground: 28 56% 94%; 

    --card: 220 10% 15%; 
    --card-foreground: 28 56% 94%;

    --popover: 220 10% 15%;
    --popover-foreground: 28 56% 94%;

    --primary: 42 100% 50%; 
    --primary-foreground: 220 13% 10%;

    /* Assuming similar adjustments for dark mode if secondary is also too light there */
    --secondary: 42 100% 30%; /* Darker Dark Amber */
    --secondary-foreground: 42 100% 85%; /* Lighter Amber for text on new dark secondary */
    
    --muted: 220 10% 25%;
    --muted-foreground: 28 56% 70%;

    /* Adjusting accent for dark mode similarly for consistency if needed */
    --accent: 51 100% 45%; /* Slightly Darker Golden Yellow for dark mode */
    --accent-foreground: 220 13% 10%; /* Dark Gray, might need adjustment if accent becomes too dark */

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 42 100% 30%;
    --input: 42 100% 25%;
    --ring: 42 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}

/* Additional global styles from user's HTML */
.hero-bg-image {
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('/images/Hero.jpg');
  background-size: cover;
  background-position: center;
}

.newsletter-bg-image {
  background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1586348943529-beaae6c28db9?w=1470&h=800&fit=crop');
  background-size: cover;
  background-position: center;
}

/* Page Loader Styles */
.page-loader-overlay {
  position: fixed;
  inset: 0;
  background-color: hsla(var(--background) / 0.85); /* Semi-transparent background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

.page-loader-overlay.visible {
  opacity: 1;
  visibility: visible;
}
