// src/app/test-integration/page.tsx
// Página de prueba para verificar la integración completa

'use client';

import React from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { useI18nData } from '@/contexts/I18nDataContext';
import { useI18n } from '@/helpers/i18n';

export default function TestIntegrationPage() {
  const { locale, strings } = useI18n();
  const { products, tags, loading, error, isTransitioning, targetLocale, progress } = useI18nData();

  const formatPrice = (price: number, currency: string) => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price);
    } catch (error) {
      return `${currency} ${price.toFixed(2)}`;
    }
  };

  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold mb-8 text-center">
            🧪 Prueba de Integración Completa
          </h1>

          {/* Estado del sistema */}
          <div className="bg-white rounded-lg p-6 mb-8 shadow-sm">
            <h2 className="text-xl font-bold mb-4">📊 Estado del Sistema</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <strong>Idioma:</strong> {locale}
              </div>
              <div>
                <strong>Productos:</strong> {products.length}
              </div>
              <div>
                <strong>Tags:</strong> {tags.length}
              </div>
              <div>
                <strong>Estado:</strong> {
                  isTransitioning 
                    ? `🔄 Cambiando (${progress}%)` 
                    : loading 
                      ? '🔄 Cargando' 
                      : '✅ Listo'
                }
              </div>
            </div>
            
            {error && (
              <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded text-red-700">
                <strong>Error:</strong> {error}
              </div>
            )}

            {isTransitioning && targetLocale && (
              <div className="mt-4 p-3 bg-blue-100 border border-blue-300 rounded text-blue-700">
                <strong>Transicionando a:</strong> {targetLocale} ({progress}%)
              </div>
            )}
          </div>

          {/* Productos */}
          <div className="bg-white rounded-lg p-6 mb-8 shadow-sm">
            <h2 className="text-xl font-bold mb-4">📦 Productos Traducidos</h2>
            
            {loading && !isTransitioning ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-48 bg-gray-200 rounded mb-4"></div>
                    <div className="h-6 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 transition-opacity duration-300 ${
                isTransitioning ? 'opacity-50' : 'opacity-100'
              }`}>
                {products.slice(0, 6).map((product) => (
                  <div key={product.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    {product.image_src && (
                      <img 
                        src={product.image_src} 
                        alt={product.image_alt || product.name}
                        className="w-full h-48 object-cover rounded-lg mb-4"
                      />
                    )}
                    
                    <h3 className="font-bold text-lg mb-2">{product.name}</h3>
                    
                    {product.description && (
                      <p className="text-gray-600 text-sm mb-3">{product.description}</p>
                    )}
                    
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-xl font-bold text-green-600">
                        {formatPrice(product.price, product.currency)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {product.currency}
                      </span>
                    </div>
                    
                    {product.tags && product.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {product.tags.slice(0, 3).map((tag: string, index: number) => (
                          <span 
                            key={index}
                            className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                        {product.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{product.tags.length - 3} más
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-bold mb-4">🏷️ Tags Traducidos</h2>
            <div className={`flex flex-wrap gap-2 transition-opacity duration-300 ${
              isTransitioning ? 'opacity-50' : 'opacity-100'
            }`}>
              {tags.slice(0, 20).map((tag) => (
                <span 
                  key={tag.id}
                  className="bg-gray-100 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                  title={`Original: ${tag.original_name}`}
                >
                  {tag.name}
                </span>
              ))}
              {tags.length > 20 && (
                <span className="text-sm text-gray-500 px-3 py-1">
                  +{tags.length - 20} más tags...
                </span>
              )}
            </div>
          </div>

          {/* Instrucciones */}
          <div className="bg-blue-50 rounded-lg p-6 mt-8">
            <h2 className="text-xl font-bold mb-4">📋 Instrucciones de Prueba</h2>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>Cambia el idioma usando el selector en el header</li>
              <li>Observa el overlay de carga que aparece</li>
              <li>Verifica que los nombres de productos cambian</li>
              <li>Confirma que los precios cambian de moneda</li>
              <li>Revisa que los tags se traducen</li>
              <li>Prueba navegando a /catalogo para ver la integración completa</li>
            </ol>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
