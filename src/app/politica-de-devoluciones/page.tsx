
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { appStrings } from '@/helpers/strings';
import Link from 'next/link';

export default function ReturnsPolicyPage() {
  const pageStrings = appStrings.returnsPage!;
  const commonStrings = appStrings.common;
  return (
    <>
      <Header />
      <main className="container mx-auto px-6 py-12 min-h-[calc(100vh-200px)]">
        <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-8">{pageStrings.title}</h1>
        <div className="space-y-4 text-muted-foreground">
          {pageStrings.content.map((paragraph: string, index: number) => (
            <p key={index}>{paragraph}</p>
          ))}
        </div>
        <div className="mt-12 mb-8 text-center">
          <Link href="/" className="text-primary hover:text-primary/80 underline underline-offset-4 transition-colors duration-200 text-lg font-semibold">
            {commonStrings.goBackButtonLabel}
          </Link>
        </div>
      </main>
      <Footer />
    </>
  );
}

