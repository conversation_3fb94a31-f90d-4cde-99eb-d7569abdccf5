'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export default function TestFeaturedPage() {
  const [allProducts, setAllProducts] = useState<any[]>([]);
  const [featuredProducts, setFeaturedProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        
        // Obtener todos los productos
        const { data: all, error: allError } = await supabase
          .from('products')
          .select('id, name, isFeatured, isActive')
          .order('name');
          
        if (allError) {
          setError(allError.message);
          return;
        }
        
        // Obtener solo productos destacados
        const { data: featured, error: featuredError } = await supabase
          .from('products')
          .select('id, name, isFeatured, isActive')
          .eq('isActive', true)
          .eq('isFeatured', true)
          .order('name');
          
        if (featuredError) {
          setError(featuredError.message);
          return;
        }
        
        setAllProducts(all || []);
        setFeaturedProducts(featured || []);
        setLoading(false);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setLoading(false);
      }
    };
    
    loadProducts();
  }, []);

  const toggleFeatured = async (productId: string, currentValue: boolean) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ isFeatured: !currentValue })
        .eq('id', productId);
        
      if (error) {
        alert('Error updating product: ' + error.message);
        return;
      }
      
      // Recargar datos
      window.location.reload();
    } catch (err) {
      alert('Error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const toggleActive = async (productId: string, currentValue: boolean) => {
    try {
      const { error } = await supabase
        .from('products')
        .update({ isActive: !currentValue })
        .eq('id', productId);
        
      if (error) {
        alert('Error updating product: ' + error.message);
        return;
      }
      
      // Recargar datos
      window.location.reload();
    } catch (err) {
      alert('Error: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  return (
    <div className="container mx-auto px-6 py-20">
      <h1 className="text-3xl font-bold mb-8">Test Featured Products</h1>
      
      {loading && (
        <div className="text-center py-8">
          <p>Loading products...</p>
        </div>
      )}

      {error && (
        <div className="text-center py-8 text-red-600">
          <p>Error: {error}</p>
        </div>
      )}

      {!loading && !error && (
        <div className="space-y-8">
          <div>
            <h2 className="text-2xl font-semibold mb-4">
              Featured Products ({featuredProducts.length})
            </h2>
            <div className="grid gap-4">
              {featuredProducts.map((product) => (
                <div key={product.id} className="border p-4 rounded bg-green-50">
                  <h3 className="font-semibold">{product.name}</h3>
                  <p className="text-sm text-gray-600">ID: {product.id}</p>
                  <div className="flex gap-2 mt-2">
                    <span className={`px-2 py-1 rounded text-xs ${product.isFeatured ? 'bg-yellow-200' : 'bg-gray-200'}`}>
                      Featured: {product.isFeatured ? 'Yes' : 'No'}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs ${product.isActive ? 'bg-green-200' : 'bg-red-200'}`}>
                      Active: {product.isActive ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h2 className="text-2xl font-semibold mb-4">
              All Products ({allProducts.length})
            </h2>
            <div className="grid gap-4">
              {allProducts.map((product) => (
                <div key={product.id} className="border p-4 rounded">
                  <h3 className="font-semibold">{product.name}</h3>
                  <p className="text-sm text-gray-600">ID: {product.id}</p>
                  <div className="flex gap-2 mt-2">
                    <button
                      onClick={() => toggleFeatured(product.id, product.isFeatured)}
                      className={`px-2 py-1 rounded text-xs cursor-pointer ${product.isFeatured ? 'bg-yellow-200 hover:bg-yellow-300' : 'bg-gray-200 hover:bg-gray-300'}`}
                    >
                      Featured: {product.isFeatured ? 'Yes' : 'No'}
                    </button>
                    <button
                      onClick={() => toggleActive(product.id, product.isActive)}
                      className={`px-2 py-1 rounded text-xs cursor-pointer ${product.isActive ? 'bg-green-200 hover:bg-green-300' : 'bg-red-200 hover:bg-red-300'}`}
                    >
                      Active: {product.isActive ? 'Yes' : 'No'}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
