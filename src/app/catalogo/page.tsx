// src/app/catalogo/page.tsx
'use client';

import { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CatalogProductCard } from '@/components/common/catalog-product-card';
import { ProductDetailDialog } from '@/components/dialogs/product-detail-dialog';
import { TagInput } from '@/components/common/tag-input';
import { useI18n } from '@/helpers/i18n';
import { useI18nData } from '@/contexts/I18nDataContext';
import type { Product } from '@/types/product';
import { Skeleton } from '@/components/ui/skeleton';
import { ShoppingCart, X, Tag, Search } from 'lucide-react';
import { useSelector } from 'react-redux';
import { selectTotalCartQuantity, selectCartTotalPrice } from '@/store/slices/cart-slice';
import type { RootState } from '@/store/store';


const PRODUCTS_PER_PAGE = 12;

export default function CatalogPage() {
  // Usar el contexto i18n para datos multiidioma
  const { strings } = useI18n();
  const { products: allProducts, tags: availableTags, loading: isLoading, error } = useI18nData();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [bannerImageUrl, setBannerImageUrl] = useState('/images/Catalogo.jpg');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showConstructionBanner, setShowConstructionBanner] = useState(false);

  const catalogStrings = strings.catalogPage || {
    title: "Catálogo",
    bannerImageAlt: "Banner del catálogo",
    searchPlaceholder: "Buscar productos...",
    tagInputPlaceholder: "Buscar por tags (ej: Floral, Fresco, ...)",
    clearFilters: "Limpiar filtros",
    checkoutButtonLabel: "Ir al carrito",
    cartTotalSummaryPrefix: "Total:",
    filteringByName: "Filtrando por nombre:",
    filteringByTags: "Filtrando por tags:",
    noResults: "No se encontraron productos",
    emptyCart: "No hay productos disponibles",
    pagination: {
      previous: "Anterior",
      next: "Siguiente",
      page: "Página",
      of: "de"
    },
    constructionBanner: {
      title: "En construcción",
      description: "Esta funcionalidad está en desarrollo"
    }
  };

  const totalCartQuantity = useSelector((state: RootState) => selectTotalCartQuantity(state.cart));
  const cartTotalPrice = useSelector((state: RootState) => selectCartTotalPrice(state.cart));

  // Mostrar error si hay problemas con la carga de datos
  useEffect(() => {
    if (error) {
      console.error('Error loading i18n data in catalog:', error);
    }
  }, [error]);


  const filteredProducts = useMemo(() => {
    if (!allProducts) return [];

    return allProducts.filter((product) => {
      const matchesSearch = searchTerm === '' ||
        product.name.toLowerCase().includes(searchTerm.toLowerCase());

      const productTags = Array.isArray(product.tags)
        ? product.tags
        : (typeof product.tags === 'string' ? [product.tags] : []);

      const matchesTags = selectedTags.length === 0 ||
        selectedTags.every(tag =>
          productTags.some((productTag: string) =>
            String(productTag).toLowerCase() === tag.toLowerCase()
          )
        );

      return matchesSearch && matchesTags;
    });
  }, [allProducts, searchTerm, selectedTags]);

  const totalPages = Math.ceil(filteredProducts.length / PRODUCTS_PER_PAGE);

  const currentProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * PRODUCTS_PER_PAGE;
    const endIndex = startIndex + PRODUCTS_PER_PAGE;
    return filteredProducts.slice(startIndex, endIndex);
  }, [filteredProducts, currentPage]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setCurrentPage(1);
  };

  const handleTagsChange = (tags: string[]) => {
    setSelectedTags(tags);
    setCurrentPage(1);
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
    setCurrentPage(1);
  };

  const goToPreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages || 1));
  };

  const handleCardClick = (product: Product) => {
    setSelectedProduct(product);
    setIsDialogOpen(true);
  };

  const renderSkeletons = () => {
    return Array.from({ length: PRODUCTS_PER_PAGE }).map((_, index) => (
      <div key={index} className="flex flex-col space-y-3 min-w-[20rem]">
        <Skeleton className="h-[200px] w-full rounded-xl min-w-[20rem]" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-[200px]" />
          <Skeleton className="h-4 w-[150px]" />
          <Skeleton className="h-4 w-[100px]" />
        </div>
      </div>
    ));
  };

  return (
    <>
      {showConstructionBanner && (
        <Alert className="fixed top-4 left-4 right-4 w-auto z-[100] p-4 rounded-lg shadow-lg bg-primary text-primary-foreground border-none">
          <div className="flex items-start">
            <div className="flex-grow">
              <AlertTitle className="font-bold">{catalogStrings.constructionBanner.title}</AlertTitle>
              <AlertDescription className="mt-1">
                {catalogStrings.constructionBanner.description}
              </AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground"
              onClick={() => setShowConstructionBanner(false)}
              aria-label="Cerrar aviso"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </Alert>
      )}
      <Header />
      <main className="min-h-screen bg-background">
        <section className="relative w-full h-64 md:h-80 mb-8">
          <Image
            src={bannerImageUrl}
            alt={catalogStrings.bannerImageAlt}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-4">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white text-center drop-shadow-lg font-serif">
              {catalogStrings.title}
            </h1>
          </div>
        </section>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="mb-10 flex flex-col items-stretch gap-6 md:flex-row md:items-start md:justify-between md:gap-8 xl:items-center">

            <div className="w-full space-y-4 md:flex-grow xl:flex xl:flex-row xl:items-center xl:gap-4 xl:space-y-0">
              <div className="flex flex-col gap-4 sm:flex-row sm:items-center xl:flex-1">
                <div className="flex-grow">
                  <Input
                    type="text"
                    placeholder={catalogStrings.searchPlaceholder}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className="w-full border-border text-sm h-11 p-2"
                  />
                </div>
                {(searchTerm || selectedTags.length > 0) && (
                  <Button
                    variant="outline"
                    onClick={handleClearFilters}
                    className="whitespace-nowrap sm:flex-shrink-0 border-border"
                  >
                    {catalogStrings.clearFilters || 'Limpiar filtros'}
                  </Button>
                )}
              </div>
              <div className="w-full xl:flex-1">
                <TagInput
                  tags={selectedTags}
                  onTagsChange={handleTagsChange}
                  placeholder={catalogStrings.tagInputPlaceholder || 'Buscar por tags (ej: Floral, Fresco, ...)'}
                  availableTags={availableTags.map(tag => tag.name)}
                />
              </div>
            </div>

            <div className="w-full flex flex-col items-center gap-2 md:w-auto md:items-end md:flex-shrink-0">
              <Link href="/checkout" passHref className="w-full md:w-auto">
                <Button
                  size="lg"
                  className="w-full md:w-auto rounded-full h-11 px-6"
                  disabled={totalCartQuantity === 0}
                  onClick={() => {
                    if (totalCartQuantity > 0) {
                       // setShowConstructionBanner(true); // Logic handled on checkout page now
                    }
                  }}
                >
                  <ShoppingCart className="h-5 w-5" />
                  <span>{catalogStrings.checkoutButtonLabel}</span>
                  {totalCartQuantity > 0 && (
                    <Badge
                      variant="default"
                      className="ml-2 h-5 w-5 min-w-[1.25rem] rounded-full p-0 flex items-center justify-center text-xs bg-green-500 text-white"
                    >
                      {totalCartQuantity}
                    </Badge>
                  )}
                </Button>
              </Link>
              {totalCartQuantity > 0 && (
                <span className="text-sm text-foreground font-medium">
                  {catalogStrings.cartTotalSummaryPrefix} ${cartTotalPrice.toFixed(2)}
                </span>
              )}
            </div>
          </div>

          {(searchTerm || selectedTags.length > 0) && (
            <div className="mb-8 flex flex-col gap-2 text-sm text-muted-foreground">
              {searchTerm && (
                <div className="flex flex-wrap gap-x-2 items-center">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{catalogStrings.filteringByName}</span>
                  <Badge variant="secondary">{searchTerm}</Badge>
                </div>
              )}
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-x-2 items-center">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{catalogStrings.filteringByTags}</span>
                  {selectedTags.map(tag => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {tag}
                      <button
                        onClick={() => handleTagsChange(selectedTags.filter(t => t !== tag))}
                        className="ml-1 p-0.5 rounded-full hover:bg-accent"
                        aria-label={`Quitar tag ${tag}`}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          )}

          {error && (
            <div className="mb-8">
              <Alert className="border-red-200 bg-red-50">
                <AlertTitle className="text-red-800">Error al cargar productos</AlertTitle>
                <AlertDescription className="text-red-600">
                  {error}. Por favor, recarga la página o intenta más tarde.
                </AlertDescription>
              </Alert>
            </div>
          )}

          {isLoading ? (
            <div className="grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-10">
               {renderSkeletons()}
            </div>
          ) : currentProducts.length > 0 ? (
            <div className="grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-10">
              {currentProducts.map((product) => (
                <CatalogProductCard
                  key={product.id}
                  product={product}
                  onClick={() => handleCardClick(product)}
                />
              ))}
            </div>
          ) : (
             <div className="text-center text-muted-foreground py-16 text-lg">
                {filteredProducts.length === 0 && (searchTerm !== "" || selectedTags.length > 0) ? catalogStrings.noResults : catalogStrings.emptyCart}
             </div>
          )}

          {totalPages > 1 && !isLoading && currentProducts.length > 0 && (
            <div className="mt-16 flex justify-center items-center space-x-4">
              <Button
                onClick={goToPreviousPage}
                disabled={currentPage === 1}
                variant="outline"
                className="rounded-full px-6 py-3"
              >
                {catalogStrings.pagination.previous}
              </Button>
              <span className="text-muted-foreground font-medium">
                {catalogStrings.pagination.page} {currentPage} {catalogStrings.pagination.of} {totalPages}
              </span>
              <Button
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                variant="outline"
                className="rounded-full px-6 py-3"
              >
                {catalogStrings.pagination.next}
              </Button>
            </div>
          )}
        </div>
      </main>
      <Footer />
      {selectedProduct && (
        <ProductDetailDialog
          product={selectedProduct}
          isOpen={isDialogOpen}
          onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) {
              setSelectedProduct(null);
            }
          }}
        />
      )}
    </>
  );
}
