"use client";

import { ContactForm } from '@/components/forms/contact-form';
import { NewsletterSection } from '@/components/sections/newsletter-section';

export default function TestFormsPage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-20">
        <h1 className="text-4xl font-bold text-center mb-12">Test Netlify Forms</h1>
        
        <div className="grid md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-2xl font-bold mb-6">Contact Form</h2>
            <ContactForm />
          </div>
          
          <div>
            <h2 className="text-2xl font-bold mb-6">Newsletter Form</h2>
            <div className="bg-primary/10 p-8 rounded-lg">
              <NewsletterSection />
            </div>
          </div>
        </div>
        
        <div className="mt-12 p-6 bg-muted rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Testing Instructions:</h3>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Fill out either form with a valid email address</li>
            <li>Submit the form</li>
            <li>Check your Netlify dashboard for form submissions</li>
            <li>Verify that the forms are detected in your Netlify site settings</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
