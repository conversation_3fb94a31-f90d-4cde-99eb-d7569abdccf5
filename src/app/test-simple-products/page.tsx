'use client';

import { SimpleI18nDataProvider, useSimpleI18nData } from '@/contexts/SimpleI18nDataContext';

function TestContent() {
  const { products, loading, error } = useSimpleI18nData();

  console.log('🧪 TestContent:', { 
    productsCount: products?.length || 0, 
    loading, 
    error,
    firstProduct: products?.[0]
  });

  return (
    <div className="container mx-auto px-6 py-20">
      <h1 className="text-3xl font-bold mb-8">Test Simple Products Page</h1>
      
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Debug Info:</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
          <p><strong>Error:</strong> {error || 'None'}</p>
          <p><strong>Products Count:</strong> {products?.length || 0}</p>
        </div>
      </div>

      {loading && (
        <div className="text-center py-8">
          <p>Loading products...</p>
        </div>
      )}

      {error && (
        <div className="text-center py-8 text-red-600">
          <p>Error: {error}</p>
        </div>
      )}

      {!loading && !error && products && products.length > 0 && (
        <div className="grid gap-4">
          <h2 className="text-xl font-semibold">Products:</h2>
          {products.map((product: any) => (
            <div key={product.id} className="border p-4 rounded">
              <h3 className="font-semibold">{product.name}</h3>
              <p className="text-gray-600">{product.description}</p>
              <p className="text-green-600 font-bold">{product.price}</p>
            </div>
          ))}
        </div>
      )}

      {!loading && !error && (!products || products.length === 0) && (
        <div className="text-center py-8">
          <p>No products found</p>
        </div>
      )}
    </div>
  );
}

export default function TestSimpleProductsPage() {
  return (
    <SimpleI18nDataProvider>
      <TestContent />
    </SimpleI18nDataProvider>
  );
}
