// src/app/pagar/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Loader2, CheckCircle, XCircle, CreditCard, ShoppingCart } from 'lucide-react';
import { useI18n } from '@/helpers/i18n';
import type { RootState, AppDispatch } from '@/store/store';
import {
  selectCartItems,
  selectCartTotalPrice,
  clearCart,
  type CartItem
} from '@/store/slices/cart-slice';
import {
  selectShippingCostValue,
  selectTaxAmountValue,
  selectShippingStatus,
  selectTaxStatus
} from '@/store/slices/checkout-slice';
import { useToast } from '@/hooks/use-toast';

// PayPal
import { PayPalScriptProvider, PayPalButtons } from "@paypal/react-paypal-js";
import type {
  PayPalScriptOptions,
  OnApproveData,
  CreateOrderData,
  CreateOrderActions,
  OnApproveActions
} from "@paypal/paypal-js";
import { capturePaypalOrder } from '@/ai/flows/payment-flows'; // Placeholder

// Stripe
import { loadStripe, type StripeError } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { createStripePaymentIntent } from '@/ai/flows/payment-flows'; // Placeholder


const paypalClientId = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || "sb"; // Default to sandbox if not set
const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || "";

const stripePromise = stripePublishableKey ? loadStripe(stripePublishableKey) : null;

// PayPal options will be configured dynamically based on locale


// Stripe Elements Wrapper Component
function StripeElementsWrapper({
  amount,
  onPaymentSuccess,
  onPaymentError
}: {
  amount: number,
  onPaymentSuccess: (details: any) => void,
  onPaymentError: (message: string) => void
}) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { strings: appStrings, localeConfig } = useI18n();
  const paymentStrings = appStrings.paymentPage;

  useEffect(() => {
    const fetchClientSecret = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/.netlify/functions/create-payment-intent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            amount,
            currency: localeConfig.currency.toLowerCase(),
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to create payment intent');
        }

        const data = await response.json();
        setClientSecret(data.clientSecret);
      } catch (err) {
        console.error('Error creating payment intent:', err);
        setError('No se pudo inicializar el pago. Por favor, inténtalo de nuevo.');
        onPaymentError('No se pudo inicializar el pago');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientSecret();
  }, [amount, onPaymentError]);

  if (isLoading) {
    return <div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>;
  }

  if (error) {
    return <div className="text-destructive p-4 bg-destructive/10 rounded-md">{error}</div>;
  }

  if (!clientSecret) {
    return <div className="text-destructive p-4 bg-destructive/10 rounded-md">Error al cargar el formulario de pago. Por favor, actualiza la página.</div>;
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret,
        appearance: {
          theme: 'stripe',
        },
        locale: localeConfig.stripeLocale as any, // Configurar Stripe según idioma detectado
      }}
    >
      <StripeCheckoutForm
        amount={amount}
        onPaymentSuccess={onPaymentSuccess}
        onPaymentError={onPaymentError}
        clientSecret={clientSecret}
      />
    </Elements>
  );
}

// Stripe Checkout Form Component
function StripeCheckoutForm({
  amount,
  onPaymentSuccess,
  onPaymentError,
  clientSecret
}: {
  amount: number,
  onPaymentSuccess: (details: any) => void,
  onPaymentError: (message: string) => void,
  clientSecret: string
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { strings: appStrings, localeConfig } = useI18n();
  const paymentStrings = appStrings.paymentPage;

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!stripe || !elements) {
      onPaymentError(paymentStrings.stripeUnavailable);
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/pagar?payment_confirmed=true`,
        },
        redirect: "if_required"
      });

      if (error) {
        setErrorMessage(error.message || paymentStrings.stripePaymentError);
        onPaymentError(error.message || paymentStrings.stripePaymentError);
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        onPaymentSuccess({
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount
        });
      } else if (paymentIntent) {
        setErrorMessage(`Estado del pago: ${paymentIntent.status}`);
        onPaymentError(`Estado del pago: ${paymentIntent.status}`);
      } else {
        setErrorMessage(paymentStrings.stripePaymentError);
        onPaymentError(paymentStrings.stripePaymentError);
      }
    } catch (e: any) {
      setErrorMessage(e.message || paymentStrings.stripePaymentError);
      onPaymentError(e.message || paymentStrings.stripePaymentError);
    }
    setIsLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <PaymentElement
        id="payment-element"
        options={{
          layout: "tabs",
          defaultValues: {
            billingDetails: {
              address: {
                country: localeConfig.code === 'es' ? 'MX' :
                         localeConfig.code === 'en' ? 'US' :
                         localeConfig.code === 'pt' ? 'BR' :
                         localeConfig.code === 'fr' ? 'FR' :
                         localeConfig.code === 'de' ? 'DE' : 'MX',
              }
            }
          }
        }}
      />
      <Button
        type="submit"
        disabled={!stripe || isLoading}
        className="w-full"
        size="lg"
      >
        {isLoading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <CreditCard className="mr-2 h-4 w-4" />
        )}
        {isLoading ? paymentStrings.processingPayment : `${paymentStrings.payButton} $${amount.toFixed(2)}`}
      </Button>
      {errorMessage && <p className="text-sm text-destructive">{errorMessage}</p>}
    </form>
  );
}


export default function PaymentPage() {
  const { strings: appStrings, localeConfig } = useI18n();
  const paymentStrings = appStrings.paymentPage;
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const { toast } = useToast();

  const cartItems = useSelector((state: RootState) => selectCartItems(state.cart));
  const cartSubtotal = useSelector((state: RootState) => selectCartTotalPrice(state.cart));
  const shippingCost = useSelector(selectShippingCostValue);
  const taxAmount = useSelector(selectTaxAmountValue);
  const shippingStatus = useSelector(selectShippingStatus);
  const taxStatus = useSelector(selectTaxStatus);

  const [isProcessing, setIsProcessing] = useState(false);

  const grandTotal = cartSubtotal + shippingCost + taxAmount;
  const isLoadingTotals = shippingStatus === 'loading' || taxStatus === 'loading';

  useEffect(() => {
    if (cartItems.length === 0 && shippingStatus !== 'loading' && taxStatus !== 'loading') {
      router.replace('/catalogo'); // Redirect to catalog if cart is empty
    }
  }, [cartItems, shippingStatus, taxStatus, router]);

  const handlePaymentSuccess = (details: any, method: 'PayPal' | 'Stripe') => {
    setIsProcessing(false);
    console.log(`${method} Payment Successful:`, details);
    toast({
      title: paymentStrings.paymentSuccessfulTitle,
      description: paymentStrings.paymentSuccessfulDescription,
      action: <CheckCircle className="text-green-500" />,
    });
    dispatch(clearCart());
    router.push('/'); // Redirect to home or an order confirmation page
  };

  const handlePaymentError = (error: any, method: 'PayPal' | 'Stripe') => {
    setIsProcessing(false);
    console.error(`${method} Payment Error:`, error);
    let errorMessage = paymentStrings.paymentFailedDescription;
    if (typeof error === 'string') {
        errorMessage = error;
    } else if (error && typeof error.message === 'string') {
        errorMessage = error.message;
    }
    toast({
      title: paymentStrings.paymentFailedTitle,
      description: errorMessage,
      variant: "destructive",
      action: <XCircle className="text-red-500" />,
    });
  };


  // PayPal createOrder
  const createPaypalOrder = async (data: Record<string, unknown>, actions: CreateOrderActions): Promise<string> => {
    setIsProcessing(true);
    try {
      const orderId = await actions.order.create({
        intent: 'CAPTURE', // Agregando el intent requerido
        purchase_units: [
          {
            amount: {
              currency_code: localeConfig.currency,
              value: grandTotal.toFixed(2),
              breakdown: {
                item_total: { currency_code: localeConfig.currency, value: cartSubtotal.toFixed(2) },
                shipping: { currency_code: localeConfig.currency, value: shippingCost.toFixed(2) },
                tax_total: { currency_code: localeConfig.currency, value: taxAmount.toFixed(2) },
              }
            },
            items: cartItems.map(item => ({
              name: item.product.name,
              quantity: item.quantity.toString(),
              unit_amount: {
                currency_code: localeConfig.currency,
                value: parseFloat(item.product.price.replace('$', '')).toFixed(2)
              },
              category: "PHYSICAL_GOODS" // Otra categoría apropiada
            }))
          },
        ]
      });
      return orderId;
    } catch (error) {
      console.error("PayPal createOrder error:", error);
      handlePaymentError(paymentStrings.paypalOrderCreationError, "PayPal");
      return "";
    }
  };

  // PayPal onApprove
  const onPaypalApprove = async (data: OnApproveData, actions: OnApproveActions) => {
    try {
      // const details = await actions.order.capture(); // This is the actual capture
      // For stub:
      const captureResponse = await capturePaypalOrder({ orderID: data.orderID });
      if (captureResponse.status === 'COMPLETED_STUB') {
        handlePaymentSuccess({ orderID: data.orderID, payerID: data.payerID, captureDetails: captureResponse }, "PayPal");
      } else {
        throw new Error(paymentStrings.paypalCaptureError);
      }
    } catch (error) {
      handlePaymentError(error, "PayPal");
    } finally {
      setIsProcessing(false);
    }
  };

  if (cartItems.length === 0) {
    return (
      <>
        <Header />
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 min-h-[calc(100vh-200px)] flex flex-col items-center justify-center text-center">
          <ShoppingCart className="h-16 w-16 text-muted-foreground mb-6" />
          <h1 className="text-3xl font-bold text-foreground mb-4">{appStrings.checkoutPage.emptyCartMessage}</h1>
          <Link href="/catalogo" passHref>
            <Button size="lg" className="rounded-full">
              {appStrings.checkoutPage.continueShoppingButton}
            </Button>
          </Link>
        </main>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-10 text-center">{paymentStrings.title}</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {/* Order Summary */}
          <div className="md:col-span-1 md:order-2">
            <Card className="shadow-lg sticky top-24">
              <CardHeader>
                <CardTitle className="text-xl">{paymentStrings.orderSummaryTitle}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {cartItems.map((item: CartItem) => (
                  <div key={item.product.id} className="flex justify-between items-center text-sm">
                    <div className="flex items-center gap-2">
                       <Image src={item.product.imageSrc} alt={item.product.name} width={40} height={40} className="rounded-md object-cover"/>
                       <div>
                           <p className="font-medium text-foreground">{item.product.name} (x{item.quantity})</p>
                           <p className="text-xs text-muted-foreground">{item.product.price}</p>
                       </div>
                    </div>
                    <span className="text-foreground font-medium">
                        ${(parseFloat(item.product.price.replace('$', '')) * item.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
                <Separator/>
                <div className="flex justify-between text-muted-foreground">
                  <span>{paymentStrings.subtotalLabel}</span>
                  <span className="font-medium text-foreground">${cartSubtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>{paymentStrings.shippingLabel}</span>
                  {shippingStatus === 'loading' ? <Skeleton className="h-5 w-16" /> : <span className="font-medium text-foreground">${shippingCost.toFixed(2)}</span>}
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>{paymentStrings.taxesLabel}</span>
                  {taxStatus === 'loading' ? <Skeleton className="h-5 w-16" /> : <span className="font-medium text-foreground">${taxAmount.toFixed(2)}</span>}
                </div>
                <Separator />
                <div className="flex justify-between text-xl font-bold text-foreground">
                  <span>{paymentStrings.totalLabel}</span>
                  {isLoadingTotals ? <Skeleton className="h-6 w-24" /> : <span>${grandTotal.toFixed(2)}</span>}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods */}
          <div className="md:col-span-2 md:order-1">
             <h2 className="text-2xl font-semibold text-foreground mb-6">{paymentStrings.selectPaymentMethod}</h2>
            {isLoadingTotals ? (
                <div className="space-y-4">
                    <Skeleton className="h-12 w-full" />
                    <Skeleton className="h-32 w-full" />
                </div>
            ) : (
            <Tabs defaultValue="stripe" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="stripe" disabled={isProcessing || !stripePromise}>
                    <CreditCard className="mr-2 h-4 w-4"/> {paymentStrings.payWithCard}
                </TabsTrigger>
                <TabsTrigger value="paypal" disabled={isProcessing || !paypalClientId || paypalClientId === "YOUR_PAYPAL_CLIENT_ID_HERE"}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-paypal mr-2" viewBox="0 0 16 16">
                        <path d="M14.06 3.713c.124-.301.248-.602.372-.903h-2.146c-.136.34-.277.672-.425.993H14.06zm-2.497.032c.17-.42.35-.823.538-1.207H8.71l-.453 1.207h3.302zm-2.71-.02c.185-.479.38-.938.582-1.37H5.992l-.452 1.37h3.39zm-2.914-.06c.188-.506.388-.991.597-1.458H1.516C1.29 2.79 1.069 3.21.862 3.685h3.36zm10.07 1.906c-.176.007-.34.042-.493.097a1.24 1.24 0 0 0-.1.041c-.41.16-.78.395-1.083.685-.318.308-.58.66-.762 1.037a2.96 2.96 0 0 0-.232.876c-.03.21-.04.428-.032.652.007.18.03.358.064.537.169.88.636 1.612 1.312 *************.3.198.463.276.**************.095.04.097.035.197.063.3.083.153.03.31.049.47.057h.19c.406-.015.793-.09 1.136-.22a3.04 3.04 0 0 0 .717-.362c.28-.168.52-.384.713-.636.22-.288.39-.617.496-.97.07-.23.11-.468.123-.71.022-.402-.032-.807-.16-1.19-.15-.443-.385-.84-.69-1.176a2.7 2.7 0 0 0-1.037-.886c-.313-.15-.66-.245-1.02-.277zm.137.806c.312.034.6.138.84.297.19.126.35.285.472.466.14.208.23.447.266.703.032.22.032.446.007.673-.028.262-.093.516-.2.758a1.8 1.8 0 0 1-.472.653c-.22.21-.487.364-.78.452a1.8 1.8 0 0 1-2.16-.02c-.27-.113-.506-.285-.69-.5-.19-.22-.33-.48-.408-.766-.086-.315-.1-.643-.05-.966.042-.27.13-.53.258-.77.145-.27.34-.504.578-.69.27-.21.59-.355.93-.42.358-.067.726-.07 1.09-.01z"/>
                        <path d="m2.82 5.513-.818 2.452c.22-.06.45-.096.686-.11.28-.015.562.004.84.05.37.06.72.194 1.028.39.12.075.23.16.332.25.098.088.183.188.253.298.08.128.14.263.18.403.04.142.06.29.06.443 0 .43-.147.81-.425 1.093-.27.274-.65.434-1.1.449-.18.006-.36-.012-.53-.056a1.92 1.92 0 0 1-.41-.147c-.14-.07-.267-.157-.38-.26a1.67 1.67 0 0 1-.28-.352c-.09-.145-.16-.3-.208-.458a1.37 1.37 0 0 1-.05-.472c.01-.2.06-.394.1********************************************************************************013.008 0 .017-.002.025-.004l.018-.004c.003 0 .005 0 .008-.002l.005-.002a.02.02 0 0 1 .005-.002c.002 0 .003 0 .004-.002L4.4 5.05l-.933 2.798H2.146C2.01 7.51 1.895 7.18.862 ******************************************** ************************************************************************************************************************************************ *******************************************************************************11-.19-.18-.27-.03-.04-.05-.07-.08-.1L3.05 8.114.78 10.29h.337zM12.21 11.6c-.016.205.023.41.1*********.**********.***********.**********.***********.*******************************************************************************65a1.3 1.3 0 0 ****************************************************************************************************************************************************************5a1.33 1.33 0 0 0-.12.64z"/>
                    </svg>
                     {paymentStrings.payWithPayPal}
                </TabsTrigger>
              </TabsList>
              <TabsContent value="stripe" className="pt-6">
                {stripePromise && stripePublishableKey && stripePublishableKey !== "YOUR_STRIPE_PUBLISHABLE_KEY_HERE" ? (
                  <StripeElementsWrapper
                    amount={grandTotal}
                    onPaymentSuccess={(details) => handlePaymentSuccess(details, "Stripe")}
                    onPaymentError={(message) => handlePaymentError(message, "Stripe")}
                  />
                ) : (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertTitle>{paymentStrings.stripeUnavailable}</AlertTitle>
                    <AlertDescription>{paymentStrings.stripeSetupError}</AlertDescription>
                  </Alert>
                )}
              </TabsContent>
              <TabsContent value="paypal" className="pt-6">
                 {paypalClientId && paypalClientId !== "YOUR_PAYPAL_CLIENT_ID_HERE" ? (
                    <PayPalScriptProvider options={{
                      clientId: paypalClientId,
                      currency: localeConfig.currency,
                      intent: "capture",
                      locale: localeConfig.paypalLocale,
                    }}>
                      <PayPalButtons
                        style={{ layout: "vertical", label: "pay", tagline: false }}
                        createOrder={createPaypalOrder}
                        onApprove={onPaypalApprove}
                        onError={(err) => handlePaymentError(err, "PayPal")}
                        onCancel={() => {
                            toast({ title: "Pago cancelado", description: "Has cancelado el proceso de pago con PayPal.", variant: "default" });
                            setIsProcessing(false);
                        }}
                        disabled={isProcessing || isLoadingTotals}
                      />
                    </PayPalScriptProvider>
                  ) : (
                    <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertTitle>{paymentStrings.paypalUnavailable}</AlertTitle>
                        <AlertDescription>{paymentStrings.stripeSetupError}</AlertDescription>
                    </Alert>
                  )}
              </TabsContent>
            </Tabs>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
