
// src/app/checkout/page.tsx
'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSelector, useDispatch } from 'react-redux';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { X, ShoppingCart, Minus } from 'lucide-react';
import { CheckoutInfoForm } from '@/components/forms/checkout-info-form';
import { useI18n } from '@/helpers/i18n';
import type { RootState, AppDispatch } from '@/store/store';
import { selectCartItems, selectCartTotalPrice, removeItemFromCart, updateItemQuantity, CartItem } from '@/store/slices/cart-slice';
import {
  fetchShippingCost,
  fetchTaxes,
  selectShippingCostValue,
  selectTaxAmountValue,
  selectShippingStatus,
  selectTaxStatus
} from '@/store/slices/checkout-slice';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';


export default function CheckoutPage() {
  const { strings: appStrings } = useI18n();
  const checkoutStrings = appStrings.checkoutPage || {
    title: "Checkout",
    emptyCartMessage: "Tu carrito está vacío",
    continueShoppingButton: "Continuar comprando",
    yourOrder: "Tu pedido",
    unitPrice: "Precio unitario:",
    orderSummary: {
      title: "Resumen del pedido",
      subtotal: "Subtotal",
      shipping: "Envío",
      taxes: "Impuestos",
      grandTotal: "Total"
    },
    toast: {
      itemRemovedTitle: "Producto eliminado",
      itemRemovedDescription: "El producto ha sido eliminado del carrito",
      quantityExceedsStockTitle: "Cantidad no disponible",
      quantityExceedsStockDescription: "Stock disponible:"
    },
    constructionBanner: {
      title: "En construcción",
      description: "Esta funcionalidad está en desarrollo"
    },
    aria: {
      productImage: "Imagen del producto"
    }
  };

  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const cartItems = useSelector((state: RootState) => selectCartItems(state.cart));
  const cartSubtotal = useSelector((state: RootState) => selectCartTotalPrice(state.cart));

  const shippingCost = useSelector(selectShippingCostValue);
  const taxAmount = useSelector(selectTaxAmountValue);
  const shippingStatus = useSelector(selectShippingStatus);
  const taxStatus = useSelector(selectTaxStatus);

  const { toast } = useToast();
  const [showConstructionBanner, setShowConstructionBanner] = useState(false);
  const [customerInfoSubmitted, setCustomerInfoSubmitted] = useState(false);

  useEffect(() => {
    if (cartItems.length > 0) {
      dispatch(fetchShippingCost(cartItems));
      dispatch(fetchTaxes({ subtotal: cartSubtotal, items: cartItems }));
    }
  }, [cartItems, cartSubtotal, dispatch]);

  const handleRemoveItem = (productId: string) => {
    dispatch(removeItemFromCart(productId));
    toast({
      title: checkoutStrings.toast.itemRemovedTitle,
      description: checkoutStrings.toast.itemRemovedDescription,
      variant: "destructive"
    });
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(productId);
    } else {
      const itemInCart = cartItems.find(item => item.product.id === productId);
      if (itemInCart && newQuantity > itemInCart.product.stock) {
        toast({
          title: checkoutStrings.toast.quantityExceedsStockTitle,
          description: `${checkoutStrings.toast.quantityExceedsStockDescription} ${itemInCart.product.stock}.`,
          variant: "destructive"
        });
        dispatch(updateItemQuantity({ productId, quantity: itemInCart.product.stock }));
      } else {
        dispatch(updateItemQuantity({ productId, quantity: newQuantity }));
      }
    }
  };

  const grandTotal = cartSubtotal + shippingCost + taxAmount;

  const handleCustomerInfoSubmitted = (customerInfo: any) => {
    setCustomerInfoSubmitted(true);
    // Store customer info in localStorage or state management
    localStorage.setItem('customerInfo', JSON.stringify(customerInfo));
    // Redirect to payment page
    router.push('/pagar');
  };

  if (cartItems.length === 0 && shippingStatus !== 'loading' && taxStatus !== 'loading') {
    return (
      <>
        <Header />
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 min-h-[calc(100vh-200px)] flex flex-col items-center justify-center text-center">
          <ShoppingCart className="h-16 w-16 text-muted-foreground mb-6" />
          <h1 className="text-3xl font-bold text-foreground mb-4">{checkoutStrings.emptyCartMessage}</h1>
          <Link href="/catalogo" passHref>
            <Button size="lg" className="rounded-full">
              {checkoutStrings.continueShoppingButton}
            </Button>
          </Link>
        </main>
        <Footer />
      </>
    );
  }

  return (
    <>
      {showConstructionBanner && ( // This banner is now for the payment page if triggered from there
        <Alert className="fixed top-4 left-4 right-4 w-auto z-[100] p-4 rounded-lg shadow-lg bg-primary text-primary-foreground border-none">
          <div className="flex items-start">
            <div className="flex-grow">
              <AlertTitle className="font-bold">{checkoutStrings.constructionBanner.title}</AlertTitle>
              <AlertDescription className="mt-1">
                {checkoutStrings.constructionBanner.description}
              </AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground"
              onClick={() => setShowConstructionBanner(false)}
              aria-label="Cerrar aviso"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </Alert>
      )}
      <Header />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-10 text-center">{checkoutStrings.title}</h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Information Form */}
            <CheckoutInfoForm
              onInfoSubmitted={handleCustomerInfoSubmitted}
              cartItems={cartItems}
              orderTotal={grandTotal}
            />

            <h2 className="text-2xl font-semibold text-foreground">{checkoutStrings.yourOrder}</h2>
            {cartItems.map((item: CartItem) => (
              <Card key={item.product.id} className="overflow-hidden shadow-sm">
                <CardContent className="p-4 flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <div className="relative w-24 h-24 sm:w-20 sm:h-20 rounded-md overflow-hidden flex-shrink-0">
                    <Image
                      src={item.product.imageSrc}
                      alt={item.product.imageAlt || checkoutStrings.aria.productImage}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 96px, 80px"
                    />
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-lg font-medium text-foreground">{item.product.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {checkoutStrings.unitPrice} {item.product.price}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2 mt-2 sm:mt-0 flex-shrink-0">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                      onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                      aria-label={`Reducir cantidad de ${item.product.name}`}
                    >
                      <Minus className="h-3.5 w-3.5" />
                    </Button>
                    <span className="text-sm font-medium w-8 text-center" aria-live="polite">{item.quantity}</span>
                  </div>
                  <div className="text-md font-semibold text-foreground mt-2 sm:mt-0 sm:ml-auto flex-shrink-0">
                    ${(parseFloat(item.product.price.replace('$', '')) * item.quantity).toFixed(2)}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-destructive hover:text-destructive/80 ml-2 sm:ml-0"
                    onClick={() => handleRemoveItem(item.product.id)}
                    title={`Eliminar ${item.product.name} del carrito`}
                    aria-label={`Eliminar ${item.product.name} del carrito`}
                  >
                     <X className="h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="lg:col-span-1">
            <Card className="shadow-lg sticky top-24">
              <CardHeader>
                <CardTitle className="text-xl">{checkoutStrings.orderSummary.title}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between text-muted-foreground">
                  <span>{checkoutStrings.orderSummary.subtotal}</span>
                  <span className="font-medium text-foreground">${cartSubtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>{checkoutStrings.orderSummary.shipping}</span>
                  {shippingStatus === 'loading' ? (
                    <Skeleton className="h-5 w-20" />
                  ) : (
                    <span className="font-medium text-foreground">${shippingCost.toFixed(2)}</span>
                  )}
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>{checkoutStrings.orderSummary.taxes}</span>
                   {taxStatus === 'loading' ? (
                    <Skeleton className="h-5 w-20" />
                  ) : (
                    <span className="font-medium text-foreground">${taxAmount.toFixed(2)}</span>
                  )}
                </div>
                <Separator />
                <div className="flex justify-between text-xl font-bold text-foreground">
                  <span>{checkoutStrings.orderSummary.grandTotal}</span>
                   {(shippingStatus === 'loading' || taxStatus === 'loading') ? (
                     <Skeleton className="h-6 w-24" />
                   ) : (
                    <span>${grandTotal.toFixed(2)}</span>
                   )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
