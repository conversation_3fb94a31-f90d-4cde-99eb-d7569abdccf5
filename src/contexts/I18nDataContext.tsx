// src/contexts/I18nDataContext.tsx
// Contexto global para manejar datos multiidioma con transiciones suaves

'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { useI18n } from '@/helpers/i18n';
import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de idiomas a monedas y regiones
const LOCALE_TO_CURRENCY: Record<string, string> = {
  'es': 'MXN',
  'en': 'USD', 
  'pt': 'BRL',
  'fr': 'EUR',
  'de': 'EUR'
};

const LOCALE_TO_REGION: Record<string, string> = {
  'es': 'MX',
  'en': 'US',
  'pt': 'BR', 
  'fr': 'EU',
  'de': 'EU'
};

interface I18nDataState {
  products: any[];
  featuredProducts: any[];
  tags: any[];
  loading: boolean;
  error: string | null;
  isTransitioning: boolean;
  targetLocale: string | null;
  progress: number;
}

interface I18nDataContextType extends I18nDataState {
  changeLocale: (newLocale: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

const I18nDataContext = createContext<I18nDataContextType | undefined>(undefined);

export function I18nDataProvider({ children }: { children: React.ReactNode }) {
  const { locale, setLocale } = useI18n();
  const [state, setState] = useState<I18nDataState>({
    products: [],
    featuredProducts: [],
    tags: [],
    loading: true,
    error: null,
    isTransitioning: false,
    targetLocale: null,
    progress: 0
  });

  console.log('🏗️ I18nDataProvider: Rendered with locale:', locale);

  const fetchData = useCallback(async (targetLocale: string, showTransition = false) => {
    try {
      if (showTransition) {
        setState(prev => ({
          ...prev,
          isTransitioning: true,
          targetLocale,
          progress: 0,
          error: null
        }));
      }

      console.log('🌍 I18nDataProvider: Fetching data for locale:', targetLocale);

      const currency = LOCALE_TO_CURRENCY[targetLocale] || 'MXN';
      const region = LOCALE_TO_REGION[targetLocale] || 'MX';

      // Progreso: 20%
      if (showTransition) {
        setState(prev => ({ ...prev, progress: 20 }));
        await new Promise(resolve => setTimeout(resolve, 150));
      }

      // 1. Obtener productos base (solo activos)
      console.log('📊 I18nDataProvider: Querying products...');
      const { data: baseProducts, error: productsError } = await supabase
        .from('products')
        .select('*')
        .eq('isActive', true);

      console.log('📊 I18nDataProvider: Products query result:', {
        count: baseProducts?.length || 0,
        error: productsError?.message || null
      });

      if (productsError) throw productsError;

      // Progreso: 40%
      if (showTransition) {
        setState(prev => ({ ...prev, progress: 40 }));
        await new Promise(resolve => setTimeout(resolve, 150));
      }

      // 2. Obtener traducciones y precios en paralelo
      const productIds = baseProducts?.map(p => p.id) || [];
      
      const [translationsResult, pricesResult, baseTagsResult, tagTranslationsResult] = await Promise.all([
        supabase
          .from('product_translations')
          .select('*')
          .in('product_id', productIds)
          .eq('language_code', targetLocale),
        supabase
          .from('product_prices')
          .select('*')
          .in('product_id', productIds)
          .eq('currency_code', currency)
          .eq('region', region),
        supabase
          .from('tags')
          .select('id, name')
          .order('name'),
        supabase
          .from('tag_translations')
          .select('*')
          .eq('language_code', targetLocale)
      ]);

      // Progreso: 80%
      if (showTransition) {
        setState(prev => ({ ...prev, progress: 80 }));
        await new Promise(resolve => setTimeout(resolve, 150));
      }

      // 3. Procesar datos
      const translations = translationsResult.data || [];
      const prices = pricesResult.data || [];
      const baseTags = baseTagsResult.data || [];
      const tagTranslations = tagTranslationsResult.data || [];

      const translationMap = new Map(translations.map(t => [t.product_id, t]));
      const priceMap = new Map(prices.map(p => [p.product_id, p]));
      
      // Mapeo de tags
      const tagIdToTranslationMap = new Map(tagTranslations.map(t => [t.tag_id, t.name]));
      const tagNameToTranslationMap = new Map();
      baseTags.forEach(tag => {
        const translatedName = tagIdToTranslationMap.get(tag.id) || tag.name;
        tagNameToTranslationMap.set(tag.name, translatedName);
      });

      // Combinar productos
      const combinedProducts = baseProducts?.map(product => {
        const translation = translationMap.get(product.id);
        const priceData = priceMap.get(product.id);
        const fallbackPrice = parseFloat(product.price?.replace(/[$,]/g, '') || '0');

        // Traducir tags
        const originalTags = product.tags || [];
        const translatedTags = originalTags.map((tagName: string) => {
          return tagNameToTranslationMap.get(tagName) || tagName;
        });

        return {
          id: product.id,
          name: translation?.name || product.name,
          description: translation?.description || product.description,
          long_description: translation?.long_description || product.long_description,
          price: priceData?.price || fallbackPrice,
          currency: priceData?.currency_code || 'MXN',
          availability: product.availability,
          stock: product.stock,
          size: product.size,
          image_src: product.image_src,
          image_alt: product.image_alt,
          image_hint: product.image_hint,
          tags: translatedTags,
          gallery: product.gallery,
          isFeatured: product.isFeatured || false,
          isActive: product.isActive || true,
          created_at: product.created_at,
          updated_at: product.updated_at
        };
      }) || [];

      // Combinar tags
      const combinedTags = baseTags.map(tag => ({
        id: tag.id,
        name: tagIdToTranslationMap.get(tag.id) || tag.name,
        original_name: tag.name
      }));

      // Progreso: 100%
      if (showTransition) {
        setState(prev => ({ ...prev, progress: 100 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Separar productos destacados
      const featuredProducts = combinedProducts.filter(product => product.isFeatured);

      console.log('✅ I18nDataProvider: Data loaded successfully:', {
        products: combinedProducts.length,
        featuredProducts: featuredProducts.length,
        tags: combinedTags.length,
        locale: targetLocale
      });

      setState(prev => ({
        ...prev,
        products: combinedProducts,
        featuredProducts: featuredProducts,
        tags: combinedTags,
        loading: false,
        error: null,
        isTransitioning: false,
        targetLocale: null,
        progress: 0
      }));

    } catch (err) {
      console.error('❌ I18nDataProvider: Error fetching data:', err);
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'Error loading data',
        loading: false,
        isTransitioning: false,
        targetLocale: null,
        progress: 0
      }));
    }
  }, []);

  // Carga inicial
  useEffect(() => {
    console.log('🚀 I18nDataProvider: Initial load for locale:', locale);
    fetchData(locale, false);
  }, [locale, fetchData]);

  // Función para cambiar idioma con transición
  const changeLocale = useCallback(async (newLocale: string) => {
    if (newLocale === locale) return;

    console.log('🎨 I18nDataProvider: Changing locale with transition:', locale, '→', newLocale);

    // Cambiar el locale en el contexto i18n
    setLocale(newLocale as any);

    // Ejecutar transición suave
    await fetchData(newLocale, true);
  }, [locale, setLocale, fetchData]);

  const refreshData = useCallback(async () => {
    await fetchData(locale, false);
  }, [locale, fetchData]);

  const contextValue: I18nDataContextType = {
    ...state,
    changeLocale,
    refreshData
  };

  return (
    <I18nDataContext.Provider value={contextValue}>
      {children}
    </I18nDataContext.Provider>
  );
}

export function useI18nData() {
  const context = useContext(I18nDataContext);
  if (context === undefined) {
    throw new Error('useI18nData must be used within an I18nDataProvider');
  }
  return context;
}
