'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

interface SimpleI18nDataState {
  products: any[];
  loading: boolean;
  error: string | null;
}

interface SimpleI18nDataContextType extends SimpleI18nDataState {
  refreshData: () => Promise<void>;
}

const SimpleI18nDataContext = createContext<SimpleI18nDataContextType | undefined>(undefined);

export function SimpleI18nDataProvider({ children }: { children: React.ReactNode }) {
  console.log('🏗️ SimpleI18nDataProvider: Starting render - VERSION 3');

  const [state, setState] = useState<SimpleI18nDataState>({
    products: [],
    loading: true,
    error: null
  });

  console.log('🏗️ SimpleI18nDataProvider: State initialized');
  console.log('🔧 SimpleI18nDataProvider: About to define useEffect');

  useEffect(() => {
    console.log('🚀 SimpleI18nDataProvider: useEffect triggered');

    const loadProducts = async () => {
      try {
        console.log('📊 Loading products...');
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        const { data: products, error } = await supabase
          .from('products')
          .select('*');
          
        console.log('📊 Products loaded:', { count: products?.length || 0, error: error?.message || null });
        
        if (error) {
          setState(prev => ({ ...prev, error: error.message, loading: false }));
          return;
        }
        
        setState(prev => ({ 
          ...prev, 
          products: products || [], 
          loading: false, 
          error: null 
        }));
        
      } catch (err) {
        console.error('❌ Error loading products:', err);
        setState(prev => ({ 
          ...prev, 
          error: err instanceof Error ? err.message : 'Unknown error', 
          loading: false 
        }));
      }
    };
    
    loadProducts();
  }, []);

  console.log('🔧 SimpleI18nDataProvider: useEffect defined');

  const refreshData = async () => {
    console.log('🔄 Refreshing data...');
  };

  const contextValue: SimpleI18nDataContextType = {
    ...state,
    refreshData
  };

  return (
    <SimpleI18nDataContext.Provider value={contextValue}>
      {children}
    </SimpleI18nDataContext.Provider>
  );
}

export function useSimpleI18nData() {
  const context = useContext(SimpleI18nDataContext);
  if (context === undefined) {
    throw new Error('useSimpleI18nData must be used within a SimpleI18nDataProvider');
  }
  return context;
}
