// src/components/layout/GlobalI18nOverlay.tsx
// Overlay global para cambios de idioma en toda la aplicación

'use client';

import React from 'react';
import LoadingOverlay from '@/components/ui/LoadingOverlay';

// Importar el contexto de datos i18n si está disponible
let useI18nData: any = null;
try {
  const i18nDataModule = require('@/contexts/I18nDataContext');
  useI18nData = i18nDataModule.useI18nData;
} catch (error) {
  // El contexto no está disponible
}

export function GlobalI18nOverlay() {
  // Solo renderizar si el contexto está disponible
  if (!useI18nData) {
    return null;
  }

  const i18nDataContext = useI18nData();

  return (
    <LoadingOverlay 
      isVisible={i18nDataContext.isTransitioning}
      locale={i18nDataContext.targetLocale || 'es'}
    />
  );
}
