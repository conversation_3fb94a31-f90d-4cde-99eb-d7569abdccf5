// src/components/layout/page-loader.tsx
'use client';

import { useSelector } from 'react-redux';
import { selectIsPageLoading } from '@/store/slices/ui-slice';
import { CustomFlameIcon } from '@/components/icons/custom-flame-icon';
import { cn } from '@/lib/utils';

export function PageLoader() {
  const isPageLoading = useSelector(selectIsPageLoading);

  // This component now just renders based on the Redux state.
  // The logic to set/unset isPageLoading is in RootLayout.

  return (
    <div
      className={cn(
        'page-loader-overlay',
        isPageLoading && 'visible' // Show/hide based on Redux state
      )}
      aria-live="polite"
      aria-busy={isPageLoading}
    >
      {/* Static, non-animated logo for now */}
      <CustomFlameIcon className="h-24 w-24 text-primary" />
    </div>
  );
}
