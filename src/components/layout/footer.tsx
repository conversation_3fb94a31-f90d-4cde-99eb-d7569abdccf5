
'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useI18n } from '@/helpers/i18n';
import { CustomFlameIcon } from '@/components/icons/custom-flame-icon';

export function Footer() {
  const { strings } = useI18n();
  const siteStrings = strings.site;
  const navStrings = strings.nav;
  const footerStrings = strings.footer;
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-foreground text-background py-12">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <CustomFlameIcon className="text-primary h-8 w-8 mr-2" />
              <h3 className="text-2xl font-bold text-background">{siteStrings.name}</h3>
            </div>
            <p className="text-muted-foreground">{footerStrings.tagline}</p>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4 text-background">{footerStrings.quickLinksTitle}</h4>
            <ul className="space-y-2">
              {navStrings.links.map(link => {
                const labelKey = link.labelKey as keyof typeof navStrings;
                const linkText = navStrings[labelKey];
                return (
                  <li key={link.labelKey}>
                    <a href={link.href} className="text-muted-foreground hover:text-primary transition">
                      {typeof linkText === 'string' ? linkText : ''}
                    </a>
                  </li>
                );
              })}
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4 text-background">{footerStrings.policyLinksTitle}</h4>
            <ul className="space-y-2">
              {footerStrings.policyLinks.map((link: any) => (
                <li key={link.label}>
                  <Link href={link.href} className="text-muted-foreground hover:text-primary transition">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-bold mb-4 text-background">{footerStrings.paymentMethodsTitle}</h4>
            <div className="flex flex-wrap gap-3">
              {footerStrings.paymentMethods.map((method: any, index: number) => (
                <div
                  key={index}
                  title={footerStrings.paymentMethodLabels[method.labelKey as keyof typeof footerStrings.paymentMethodLabels]}
                  className="bg-[#222222] text-[#aaaaaa] p-1 rounded flex items-center justify-center h-7" // Contenedor con altura fija
                >
                  <Image
                    src={method.imageSrc}
                    alt={footerStrings.paymentMethodLabels[method.labelKey as keyof typeof footerStrings.paymentMethodLabels]}
                    height={20} // Guía de proporción para Next.js
                    width={35}  // Guía de proporción para Next.js
                    className="object-contain h-full w-auto" // Escala correctamente
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="border-t border-muted/30 mt-10 pt-6 text-center text-muted-foreground">
          <p>{footerStrings.copyright(currentYear)}</p>
        </div>
      </div>
    </footer>
  );
}
