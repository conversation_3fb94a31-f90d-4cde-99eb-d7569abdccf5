
"use client";

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ShoppingCart, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetClose } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { useI18n } from '@/helpers/i18n';
import { CustomFlameIcon } from '@/components/icons/custom-flame-icon';
import { LanguageSelector } from '@/components/ui/language-selector';
import { usePathname } from 'next/navigation';

export function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { strings: appStrings } = useI18n();
  const navStrings = appStrings.nav;
  const siteStrings = appStrings.site;
  const pathname = usePathname();
  const [currentHash, setCurrentHash] = useState('');

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const updateHash = () => {
      setCurrentHash(window.location.hash);
    };

    updateHash(); // Set initial hash

    window.addEventListener('hashchange', updateHash);
    // Also update on pathname change for cases where hash might be cleared by router
    // or if navigating to the same page but with a different hash programmatically
    // (though Link clicks should trigger hashchange).
    const handlePopState = () => { // Handles browser back/forward
        updateHash();
    };
    window.addEventListener('popstate', handlePopState);


    return () => {
      window.removeEventListener('hashchange', updateHash);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [pathname]); // Re-run when pathname changes to reset initial hash if needed

  const handleLinkClick = (hash: string) => {
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
    // Only set hash for anchor links
    if (hash.startsWith('#')) {
      setCurrentHash(hash);
    } else {
      // For full page navigations, clear or set hash appropriately if needed
      // For now, let the browser and useEffect handle it.
      // setCurrentHash(''); // Or determine specific hash if navigating to an anchor on another page
    }
  };

  const renderNavLinks = (isMobile: boolean) => {
    return navStrings.links.map((link) => {
      const linkLabelKey = link.labelKey as keyof typeof navStrings;
      const linkContentRaw = navStrings[linkLabelKey];
      const linkContent = typeof linkContentRaw === 'string' ? linkContentRaw : '';
      const canonicalHref = link.href; // e.g., "/#inicio", "/#productos"

      let linkAnchorId = '';
      if (canonicalHref.startsWith('/#')) {
        linkAnchorId = canonicalHref.substring(1); // "inicio", "productos"
      }

      const currentHashId = currentHash ? currentHash.substring(1) : '';

      let isVisuallyActive = false;
      let isCurrentPageLink = false;

      if (canonicalHref === '/#inicio') {
        isVisuallyActive = pathname === '/' && (currentHash === '' || currentHashId === 'inicio');
        isCurrentPageLink = isVisuallyActive;
      } else if (canonicalHref.startsWith('/#')) {
        isVisuallyActive = pathname === '/' && currentHashId === linkAnchorId;
        isCurrentPageLink = false; // Anchor links on home page (other than #inicio) are for scroll, not page disable
      } else {
        // Direct page links like /catalogo
        isVisuallyActive = pathname === canonicalHref;
        isCurrentPageLink = isVisuallyActive;
      }

      let hrefForLink: string;
      if (canonicalHref.startsWith('/#')) {
        hrefForLink = (pathname === '/') ? `#${linkAnchorId}` : canonicalHref;
      } else {
        hrefForLink = canonicalHref;
      }

      const clickHandler = () => handleLinkClick(canonicalHref.startsWith('/#') ? `#${linkAnchorId}` : '');

      const linkClasses = cn(
        isMobile ? "text-lg" : "",
        "text-muted-foreground hover:text-primary transition",
        isVisuallyActive && 'text-primary', // Always highlight if visually active
        isCurrentPageLink && 'pointer-events-none opacity-75' // Additionally disable if it's the exact current page link
      );

      if (isMobile) {
        return (
          <SheetClose asChild key={canonicalHref}>
            <Link
              href={hrefForLink}
              className={linkClasses}
              aria-disabled={isCurrentPageLink}
              tabIndex={isCurrentPageLink ? -1 : undefined}
              onClick={clickHandler}
            >
              {linkContent}
            </Link>
          </SheetClose>
        );
      }

      return (
        <Link
          key={canonicalHref}
          href={hrefForLink}
          className={linkClasses}
          aria-disabled={isCurrentPageLink}
          tabIndex={isCurrentPageLink ? -1 : undefined}
          onClick={clickHandler}
        >
          {linkContent}
        </Link>
      );
    });
  };

  const isCatalogPageActive = pathname === '/catalogo';
  const catalogLinkOnClick = () => {
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
    // No need to setCurrentHash for /catalogo as it's a full page nav
  };

  return (
    <header className={cn(
      "sticky top-0 z-50 transition-shadow duration-300",
      isScrolled ? "bg-card shadow-md" : "bg-transparent md:bg-card/80 md:backdrop-blur-md"
    )}>
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center" onClick={() => handleLinkClick('#inicio')}>
            <CustomFlameIcon className="text-primary h-8 w-8 mr-2" />
            <h1 className="text-2xl font-bold text-foreground">{siteStrings.name}</h1>
          </Link>

          <nav className="hidden md:flex space-x-8">
            {renderNavLinks(false)}
          </nav>

          <div className="flex items-center space-x-4">
            <LanguageSelector />
            <Link
              href="/catalogo"
              passHref
              className={cn({
                'pointer-events-none': isCatalogPageActive,
              })}
              aria-disabled={isCatalogPageActive}
              tabIndex={isCatalogPageActive ? -1 : undefined}
              onClick={catalogLinkOnClick}
            >
              <Button
                variant="default"
                title={navStrings.aria.catalogTitle}
                className={cn(
                  "font-bold text-white", // Base styles
                  "h-10 px-3", // Mobile/default size (compact)
                  "md:h-11 md:px-6", // Medium and up size (larger)
                  isCatalogPageActive ? "opacity-50" : "hover:bg-primary/90"
                )}
                disabled={isCatalogPageActive}
              >
                <ShoppingCart className="h-5 w-5" />
                <span className="ml-1.5 md:ml-2"> {/* Responsive margin for text */}
                  <span className="md:hidden">Catálogo</span> {/* Text for mobile */}
                  <span className="hidden md:inline">{navStrings.aria.catalogTitle}</span> {/* Full text for md and up */}
                </span>
              </Button>
            </Link>

            <div className="md:hidden">
              <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" aria-label={navStrings.aria.openMenu} onClick={() => setIsMobileMenuOpen(true)}>
                    <Menu className="text-muted-foreground" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[280px] bg-card p-6">
                  <div className="flex flex-col space-y-6 mt-8">
                    <SheetClose asChild>
                      <Link href="/" className="flex items-center mb-6" onClick={() => handleLinkClick('#inicio')}>
                          <CustomFlameIcon className="text-primary h-8 w-8 mr-2" />
                          <h1 className="text-2xl font-bold text-foreground">{siteStrings.name}</h1>
                      </Link>
                    </SheetClose>
                    {renderNavLinks(true)}
                    <div className="pt-4 border-t border-border">
                      <LanguageSelector />
                    </div>
                     <SheetClose asChild>
                        <Link
                          href="/catalogo"
                          className={cn(
                            "text-lg text-muted-foreground hover:text-primary transition",
                             isCatalogPageActive && 'text-primary pointer-events-none opacity-75'
                          )}
                          aria-disabled={isCatalogPageActive}
                          tabIndex={isCatalogPageActive ? -1 : undefined}
                          onClick={catalogLinkOnClick}
                        >
                            {navStrings.aria.catalogTitle}
                        </Link>
                      </SheetClose>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
