import Image from 'next/image';
import { Star, StarHalf } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface TestimonialCardProps {
  avatarSrc: string;
  name: string;
  rating: number; // e.g., 5 for 5 stars, 4.5 for 4.5 stars
  quote: string;
  imageHint?: string;
}

const renderStars = (rating: number) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const halfStar = rating % 1 !== 0;
  
  for (let i = 0; i < fullStars; i++) {
    stars.push(<Star key={`full-${i}`} className="h-5 w-5 text-accent fill-accent" />);
  }
  if (halfStar) {
    stars.push(<StarHalf key="half" className="h-5 w-5 text-accent fill-accent" />);
  }
  const emptyStars = 5 - Math.ceil(rating);
  for (let i = 0; i < emptyStars; i++) {
    stars.push(<Star key={`empty-${i}`} className="h-5 w-5 text-accent" />); // outlined star
  }
  return stars;
};

export function TestimonialCard({ avatarSrc, name, rating, quote, imageHint }: TestimonialCardProps) {
  return (
    <Card className="bg-background/80 backdrop-blur-md p-8 rounded-xl shadow-md">
      <CardContent className="p-0">
        <div className="flex items-center mb-4">
          <div className="flex-shrink-0 bg-[#222222] text-[#aaaaaa] rounded-full h-12 w-12 overflow-hidden">
            <Image 
              className="h-full w-full object-cover" 
              src={avatarSrc} 
              alt={name} 
              width={48} 
              height={48}
              data-ai-hint={imageHint}
            />
          </div>
          <div className="ml-4">
            <h4 className="text-lg font-bold text-foreground">{name}</h4>
            <div className="flex text-accent">
              {renderStars(rating)}
            </div>
          </div>
        </div>
        <p className="text-muted-foreground italic">&quot;{quote}&quot;</p>
      </CardContent>
    </Card>
  );
}
