import { Button } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Check, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PricingCardProps {
  title: string;
  price: string;
  priceSuffix: string;
  features: { text: string; included: boolean }[];
  isFeatured?: boolean;
  buttonText: string;
  headerBgClass: string;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive" | null | undefined;
  borderColor?: string;
  textColor?: string;
  priceColor?: string;
  badgeText?: string;
}

export function PricingCard({
  title,
  price,
  priceSuffix,
  features,
  isFeatured = false,
  buttonText,
  headerBgClass,
  buttonVariant = "outline",
  borderColor = 'border-secondary',
  textColor = 'text-foreground',
  priceColor = 'text-primary',
  badgeText
}: PricingCardProps) {
  return (
    <Card className={cn(
      "rounded-xl overflow-hidden shadow-lg transform hover:scale-105 transition-all duration-300 relative",
      isFeatured ? "border-2 border-primary shadow-xl bg-secondary/30" : `bg-background/30 ${borderColor}`
    )}>
      {isFeatured && badgeText && (
        <div className="absolute top-0 right-0 bg-primary text-primary-foreground text-xs font-bold px-3 py-1 rounded-bl-lg">
          {badgeText}
        </div>
      )}
      <CardHeader className={cn("py-4 px-6", headerBgClass)}>
        <CardTitle className={cn("text-xl font-bold text-center", isFeatured ? "text-primary-foreground" : textColor)}>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="text-center mb-6">
          <span className={cn("text-4xl font-bold", priceColor)}>{price}</span>
          <span className="text-muted-foreground">{priceSuffix}</span>
        </div>
        <ul className="space-y-3 mb-8">
          {features.map((feature, index) => (
            <li key={index} className={cn("flex items-center", !feature.included && "text-muted-foreground/70")}>
              {feature.included ? (
                <Check className="text-primary mr-2 h-5 w-5" />
              ) : (
                <X className="text-muted-foreground/50 mr-2 h-5 w-5" />
              )}
              <span>{feature.text}</span>
            </li>
          ))}
        </ul>
      </CardContent>
      <CardFooter className="p-6 pt-0">
        <Button 
          className={cn("w-full font-bold py-3 rounded-full transition", 
            isFeatured ? "bg-primary hover:bg-primary/90 text-primary-foreground" : "bg-secondary hover:bg-secondary/80 text-secondary-foreground"
          )}
        >
          {buttonText}
        </Button>
      </CardFooter>
    </Card>
  );
}
