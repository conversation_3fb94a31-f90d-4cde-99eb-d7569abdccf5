// src/components/common/catalog-product-card.tsx
'use client';

import { useState } from 'react';
import Image from 'next/image';
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CheckCircle2, XCircle, Tag, Minus, Plus, ShoppingCart } from 'lucide-react';
import { appStrings } from '@/helpers/strings';
import type { Product } from '@/types/product';
import { useToast } from '@/hooks/use-toast';
import { useDispatch } from 'react-redux';
import { addItemToCart } from '@/store/slices/cart-slice';
import type { AppDispatch } from '@/store/store';

interface CatalogProductCardProps {
  product: Product;
  onClick: () => void;
}

export function CatalogProductCard({ product, onClick }: CatalogProductCardProps) {
  const catalogStrings = appStrings.catalogPage!;
  const commonStrings = appStrings.common;
  const { toast } = useToast();
  const dispatch = useDispatch<AppDispatch>();
  const [quantity, setQuantity] = useState(0);

  const isAvailableForPurchase = product.availability && product.stock > 0;
  const maxQuantity = isAvailableForPurchase ? product.stock : 0;

  const sizeTranslations: { [key: string]: string } = {
    chica: "Chica",
    mediana: "Mediana",
    grande: "Grande",
  };

  const handleIncrement = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isAvailableForPurchase) {
      setQuantity((prev) => Math.min(prev + 1, maxQuantity));
    }
  };

  const handleDecrement = (e: React.MouseEvent) => {
    e.stopPropagation();
    setQuantity((prev) => Math.max(0, prev - 1));
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (quantity === 0 || !isAvailableForPurchase || quantity > maxQuantity) return;

    dispatch(addItemToCart({ product, quantity }));

    toast({
      title: `${quantity} ${product.name} ${catalogStrings.addedToCartSuffix}`,
      description: `${catalogStrings.cartTotalPrefix} $${(parseFloat(product.price.replace('$', '')) * quantity).toFixed(2)}`,
    });
    setQuantity(0);
  };

  return (
    <Card
        className="min-w-[20rem] overflow-hidden shadow-lg transition-all duration-300 hover:-translate-y-1 hover:shadow-xl group flex flex-col h-full bg-card cursor-pointer"
        onClick={onClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') onClick();}}
        aria-label={`Ver detalles de ${product.name}`}
    >
      <CardHeader className="p-0">
        <div className="aspect-[4/3] relative w-full bg-[#222222] text-[#aaaaaa]">
          <Image
            src={product.imageSrc}
            alt={product.imageAlt}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            data-ai-hint={product.imageHint}
            sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20rem"
          />
        </div>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <CardTitle className="text-lg mb-1 font-semibold text-foreground">{product.name}</CardTitle>
        <p className="text-muted-foreground text-xs mb-2 overflow-hidden line-clamp-2">{product.description}</p>
        <div className="flex items-center text-xs text-muted-foreground mb-1">
          {isAvailableForPurchase ? (
            <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
          ) : (
            <XCircle className="h-4 w-4 mr-1 text-red-500" />
          )}
          {isAvailableForPurchase ? catalogStrings.availability.available : catalogStrings.availability.unavailable}
           {isAvailableForPurchase && product.stock < 5 && product.stock > 0 && (
             <span className="ml-2 text-amber-600 font-medium">(¡Últimos {product.stock}!)</span>
           )}
        </div>
        <p className="text-xs text-muted-foreground mb-2">
          {catalogStrings.sizeLabel} {sizeTranslations[product.size.toLowerCase()] || product.size}
        </p>
      </CardContent>
      <CardFooter className="p-4 pt-2 border-t border-border/20 mt-auto">
        <div className="w-full space-y-3">
            <div className="flex justify-between items-center">
                <span className="text-primary font-bold text-lg">{product.price}</span>
            </div>

            <div className="space-y-1.5">
              <label
                htmlFor={`quantity-${product.id}`}
                className="text-xs text-muted-foreground font-medium"
                onClick={(e) => e.stopPropagation()}
              >
                {catalogStrings.quantityLabel}
              </label>
              <div className="flex flex-col items-center gap-y-2 sm:flex-row sm:justify-between sm:items-center sm:gap-x-2">
                <div className="flex items-center space-x-1">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleDecrement}
                    className="h-8 w-8 shrink-0 rounded-full"
                    disabled={!isAvailableForPurchase}
                  >
                    <Minus className="h-3.5 w-3.5" />
                  </Button>
                  <Input
                    id={`quantity-${product.id}`}
                    type="text"
                    value={quantity}
                    readOnly
                    onClick={(e) => e.stopPropagation()}
                    className="h-8 w-12 text-center bg-background focus-visible:ring-primary focus-visible:ring-1 focus-visible:ring-offset-0 px-1"
                    aria-label={catalogStrings.quantityLabel}
                    disabled={!isAvailableForPurchase}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleIncrement}
                    className="h-8 w-8 shrink-0 rounded-full"
                    disabled={!isAvailableForPurchase || quantity >= maxQuantity}
                  >
                    <Plus className="h-3.5 w-3.5" />
                  </Button>
                </div>
                <Button
                  onClick={handleAddToCart}
                  className="h-8 px-4 text-xs rounded-full"
                  disabled={quantity === 0 || !isAvailableForPurchase || quantity > maxQuantity}
                >
                  <ShoppingCart className="mr-1.5 h-3.5 w-3.5" />
                  {commonStrings.addToCart}
                </Button>
              </div>
            </div>

            {(Array.isArray(product.tags) ? product.tags : []).length > 0 && (
            <div className="flex flex-wrap gap-1 items-center justify-center text-xs pt-1">
                <Tag className="h-3 w-3 text-muted-foreground mr-0.5" />
                {(Array.isArray(product.tags) ? product.tags : []).slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="font-normal text-xs px-2 py-0.5">
                    {String(tag)}
                </Badge>
                ))}
            </div>
            )}
        </div>
      </CardFooter>
    </Card>
  );
}
