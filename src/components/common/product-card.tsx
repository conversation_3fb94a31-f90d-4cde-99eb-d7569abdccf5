import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { appStrings } from '@/helpers/strings';

interface ProductCardProps {
  imageSrc: string;
  imageAlt: string;
  title: string;
  description: string;
  price: string;
  imageHint?: string;
}

export function ProductCard({ imageSrc, imageAlt, title, description, price, imageHint }: ProductCardProps) {
  const commonStrings = appStrings.common;
  return (
    <Card className="overflow-hidden shadow-lg transition-all duration-300 hover:-translate-y-2.5 hover:shadow-xl group">
      <CardHeader className="p-0">
        <div className="aspect-[4/3] relative w-full">
          <Image 
            src={imageSrc} 
            alt={imageAlt} 
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            data-ai-hint={imageHint}
          />
        </div>
      </CardHeader>
      <CardContent className="p-6">
        <CardTitle className="text-xl mb-2">{title}</CardTitle>
        <p className="text-muted-foreground mb-4 text-sm">{description}</p>
      </CardContent>
      <CardFooter className="flex justify-between items-center p-6 pt-0">
        <span className="text-primary font-bold text-xl">{price}</span>
        <Button variant="outline" className="bg-secondary hover:bg-secondary/80 text-secondary-foreground font-bold rounded-full transition">
          {commonStrings.addToCart}
        </Button>
      </CardFooter>
    </Card>
  );
}
