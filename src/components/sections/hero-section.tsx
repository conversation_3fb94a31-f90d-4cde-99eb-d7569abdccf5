import { Button } from '@/components/ui/button';
import { appStrings } from '@/helpers/strings';

export function HeroSection() {
  const heroStrings = appStrings.heroSection;
  const commonStrings = appStrings.common;
  return (
    <section id="inicio" className="hero-bg-image min-h-screen flex items-center justify-center text-center text-white">
      <div className="container mx-auto px-6 py-20">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 drop-shadow-lg">{heroStrings.title}</h1>
        <p className="text-xl md:text-2xl mb-10 max-w-2xl mx-auto drop-shadow-sm">
          {heroStrings.subtitle}
        </p>
        <Button asChild size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground font-bold py-3 px-8 rounded-full transition duration-300 shadow-lg hover:shadow-xl">
          <a href="#productos">{commonStrings.exploreCollection}</a>
        </Button>
      </div>
    </section>
  );
}
