
"use client";
import { Phone, Mail, Facebook, Instagram } from 'lucide-react';
import { ContactForm } from '@/components/forms/contact-form';
import { useI18n } from '@/helpers/i18n';

// Inline SVG for Pinterest icon
const PinterestIcon = ({ className }: { className?: string }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path d="M12 0C5.373 0 0 5.373 0 12C0 17.095 3.205 21.385 7.586 22.93C7.489 22.093 7.332 20.702 7.457 20.092C7.574 19.533 8.181 17.168 8.181 17.168C8.181 17.168 7.873 16.571 7.873 15.643C7.873 14.305 8.771 13.234 9.779 13.234C10.671 13.234 11.073 13.956 11.073 14.715C11.073 15.602 10.465 16.833 10.121 17.485C9.755 18.186 10.303 18.75 10.937 18.75C12.185 18.75 13.081 17.396 13.081 15.36C13.081 13.548 11.746 12.037 9.82 12.037C7.465 12.037 5.893 14.084 5.893 16.011C5.893 16.842 6.273 17.433 6.582 17.811C6.657 17.904 6.674 17.993 6.643 18.104C6.574 18.361 6.473 18.785 6.438 18.925C6.394 19.103 6.264 19.154 6.096 19.04C5.16 18.437 4.5 17.075 4.5 15.634C4.5 12.964 6.453 10.5 9.971 10.5C13.016 10.5 15.5 12.608 15.5 15.469C15.5 17.716 14.265 19.424 12.391 19.424C11.576 19.424 10.997 18.922 10.997 18.235C10.997 17.612 11.365 17.009 11.365 17.009C12.116 15.307 12.546 14.208 12.546 13.614C12.546 12.838 11.928 12.581 11.928 12.581C11.928 12.581 10.767 12.713 10.767 14.247C10.767 14.923 10.68 15.462 10.68 15.462C10.019 17.918 8.987 19.734 8.987 19.734C8.987 19.734 9.336 20.764 9.488 21.208C9.772 22.025 10.619 22.995 11.13 23.363C11.419 23.571 11.707 23.776 12 24C18.627 24 24 18.627 24 12C24 5.373 18.627 0 12 0Z"/>
  </svg>
);

const contactDetailsIcons = [
  { icon: <Phone className="text-primary h-5 w-5 mr-4" />, textKey: 0 }, // Adjusted textKey
  { icon: <Mail className="text-primary h-5 w-5 mr-4" />, textKey: 1 },   // Adjusted textKey
];

export function ContactSection() {
  const { strings } = useI18n();
  const contactStrings = strings.contactSection;

  const socialLinks = [
    { icon: <Facebook className="h-5 w-5" />, href: '#', labelKey: 'facebook' as keyof typeof contactStrings.socialAriaLabels },
    { icon: <Instagram className="h-5 w-5" />, href: '#', labelKey: 'instagram' as keyof typeof contactStrings.socialAriaLabels },
    { icon: <PinterestIcon className="h-5 w-5" />, href: '#', labelKey: 'pinterest' as keyof typeof contactStrings.socialAriaLabels },
  ];

  return (
    <section id="contacto" className="py-20 bg-card">
      <div className="container mx-auto px-6">
        <div className="flex flex-col md:flex-row gap-10 md:gap-16">
          <div className="md:w-1/2">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">{contactStrings.title}</h2>
            <p className="text-muted-foreground mb-8">
              {contactStrings.subtitle}
            </p>

            <div className="space-y-4">
              {contactDetailsIcons.map((detail, index) => (
                <div key={index} className="flex items-center">
                  {detail.icon}
                  <span className="text-muted-foreground">{contactStrings.details?.[detail.textKey]?.text || detail.textKey}</span>
                </div>
              ))}
            </div>

            <div className="mt-8 flex space-x-4">
              {socialLinks.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  aria-label={contactStrings.socialAriaLabels[link.labelKey]}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-secondary hover:bg-secondary/80 w-10 h-10 rounded-full flex items-center justify-center text-secondary-foreground transition-colors"
                >
                  {link.icon}
                </a>
              ))}
            </div>
          </div>

          <div className="md:w-1/2">
            <ContactForm />
          </div>
        </div>
      </div>
    </section>
  );
}
