import { TestimonialCard } from '@/components/common/testimonial-card';
import { appStrings } from '@/helpers/strings';

export function TestimonialsSection() {
  const testimonialStrings = appStrings.testimonialsSection!;
  return (
    <section id="testimonios" className="py-20 bg-secondary/30">
      <div className="container mx-auto px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-foreground mb-16">{testimonialStrings.title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonialStrings.testimonials.map((testimonial: any, index: number) => (
            <TestimonialCard
              key={index}
              avatarSrc={testimonial.avatarSrc}
              name={testimonial.name}
              rating={testimonial.rating}
              quote={testimonial.quote}
              imageHint={testimonial.imageHint}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
