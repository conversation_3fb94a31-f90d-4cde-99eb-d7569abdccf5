
// src/components/sections/products-section.tsx
'use client';

import { useState, useMemo } from 'react';
import Link from 'next/link';
import { CatalogProductCard } from '@/components/common/catalog-product-card';
import { ProductDetailDialog } from '@/components/dialogs/product-detail-dialog';
import { useI18n } from '@/helpers/i18n';
import { useI18nData } from '@/contexts/I18nDataContext';
import type { Product } from '@/types/product';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShoppingCart, X } from 'lucide-react';
import { useSelector } from 'react-redux';
import { selectTotalCartQuantity, selectCartTotalPrice, selectCartItems } from '@/store/slices/cart-slice';
import type { RootState, CartItem } from '@/store/store';


export function ProductsSection() {
  const { strings } = useI18n();
  const productSectionStrings = strings.productsSection;
  const catalogStrings = strings.catalogPage; // For checkout button and banner strings
  const { products: i18nProducts, loading: productsLoading } = useI18nData();
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [showConstructionBanner, setShowConstructionBanner] = useState(false); // Kept for payment step

  const totalCartQuantity = useSelector((state: RootState) => selectTotalCartQuantity(state.cart));
  const cartTotalPrice = useSelector((state: RootState) => selectCartTotalPrice(state.cart));
  const cartItems = useSelector((state: RootState) => selectCartItems(state.cart));

  // Convertir los datos de la base de datos i18n a la estructura Product completa
  const productsData: Product[] = useMemo(() => {
    console.log('🔍 ProductsSection Debug:', {
      i18nProducts: i18nProducts?.length || 0,
      productsLoading,
      firstProduct: i18nProducts?.[0]
    });

    if (!i18nProducts || i18nProducts.length === 0) return [];

    return i18nProducts.slice(0, 6).map((p: any) => ({
      id: p.id,
      name: p.name,
      imageSrc: p.image_src || '/images/Featured-01.jpg',
      imageAlt: p.image_alt || p.name,
      description: p.description,
      price: typeof p.price === 'number' ? `$${p.price.toFixed(2)}` : p.price,
      availability: p.availability,
      stock: p.stock,
      reorderPoint: 0,
      size: p.size || 'Mediana',
      tags: p.tags || [],
      longDescription: p.long_description || p.description,
      gallery: p.gallery || [],
      imageHint: p.image_hint || '',
    }));
  }, [i18nProducts, productsLoading]);

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
    setIsDialogOpen(true);
  };

  const isAnyFeaturedProductInCart = useMemo(() => {
    if (!cartItems || cartItems.length === 0) {
      return false;
    }
    const featuredProductIds = productsData.map(p => p.id);
    return cartItems.some(cartItem =>
      featuredProductIds.includes(cartItem.product.id) && cartItem.quantity > 0
    );
  }, [cartItems, productsData]);

  const checkoutButtonBlock = (
    <div className="mt-12 mb-10 flex flex-col sm:flex-row items-center justify-center gap-4">
      <Link href="/checkout" passHref>
        <Button
          size="lg"
          className="rounded-full h-11 px-6"
          disabled={totalCartQuantity === 0}
        >
          <ShoppingCart className="h-5 w-5" />
          <span>{catalogStrings.checkoutButtonLabel}</span>
          {totalCartQuantity > 0 && (
            <Badge
              variant="default"
              className="ml-1 h-5 w-5 min-w-[1.25rem] rounded-full p-0 flex items-center justify-center text-xs bg-green-500 text-white"
            >
              {totalCartQuantity}
            </Badge>
          )}
        </Button>
      </Link>
      {totalCartQuantity > 0 && (
        <span className="text-sm text-foreground font-medium">
          {catalogStrings.cartTotalSummaryPrefix} ${cartTotalPrice.toFixed(2)}
        </span>
      )}
    </div>
  );

  return (
    <>
      {showConstructionBanner && ( // This banner is now for the payment step if triggered from checkout
        <Alert className="fixed top-4 left-4 right-4 w-auto z-[100] p-4 rounded-lg shadow-lg bg-primary text-primary-foreground border-none">
          <div className="flex items-start">
            <div className="flex-grow">
              <AlertTitle className="font-bold">{catalogStrings.constructionBanner.title}</AlertTitle>
              <AlertDescription className="mt-1">
                {catalogStrings.constructionBanner.description}
              </AlertDescription>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-auto text-primary-foreground hover:bg-primary/80 hover:text-primary-foreground"
              onClick={() => setShowConstructionBanner(false)}
              aria-label="Cerrar aviso"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </Alert>
      )}
      <section id="productos" className="py-20 bg-card">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-foreground mb-16">{productSectionStrings.title}</h2>

          {productsLoading ? (
            <div className="grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-10">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="flex flex-col space-y-3 min-w-[20rem]">
                  <div className="h-[200px] w-full rounded-xl bg-muted animate-pulse" />
                  <div className="space-y-2">
                    <div className="h-4 w-[200px] bg-muted animate-pulse rounded" />
                    <div className="h-4 w-[150px] bg-muted animate-pulse rounded" />
                    <div className="h-4 w-[100px] bg-muted animate-pulse rounded" />
                  </div>
                </div>
              ))}
            </div>
          ) : productsData.length > 0 ? (
            <div className="grid grid-cols-[repeat(auto-fit,minmax(20rem,1fr))] gap-10">
              {productsData.map((product) => (
                <CatalogProductCard
                  key={product.id}
                  product={product}
                  onClick={() => handleProductClick(product)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-16 text-lg">
              No hay productos disponibles en este momento.
            </div>
          )}

          {isAnyFeaturedProductInCart && checkoutButtonBlock}

        </div>
      </section>
      {selectedProduct && (
        <ProductDetailDialog
          product={selectedProduct}
          isOpen={isDialogOpen}
          onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) {
              setSelectedProduct(null);
            }
          }}
        />
      )}
    </>
  );
}

