
// src/components/sections/newsletter-section.tsx
"use client";
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import type { FormEvent } from 'react';
import { useI18n } from '@/helpers/i18n';

export function NewsletterSection() {
  const { toast } = useToast();
  const { strings } = useI18n();
  const newsletterStrings = strings.newsletterSection;
  const commonStrings = strings.common;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    const form = event.currentTarget;
    const formData = new FormData(form);
    const email = formData.get('email') as string;

    if (!email || !email.includes('@')) {
      toast({
        title: newsletterStrings.toast.errorTitle,
        description: newsletterStrings.toast.errorDescription,
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Prepare data for Netlify Forms (x-www-form-urlencoded)
    const netlifyFormData = new URLSearchParams();
    for (const pair of formData.entries()) {
      netlifyFormData.append(pair[0], pair[1] as string);
    }
    // Ensure 'form-name' is included if not already in FormData
    // (it should be if the hidden input is present)
    if (!formData.has('form-name')) {
        netlifyFormData.append('form-name', form.getAttribute('name') || 'newsletter');
    }

    try {
      const response = await fetch('/', { // Submit to current page for Netlify Forms processing
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: netlifyFormData.toString(),
      });

      if (response.ok) {
        toast({
          title: newsletterStrings.toast.successTitle,
          description: newsletterStrings.toast.successDescription,
        });
        form.reset();
      } else {
        // Netlify might return a non-ok status for various reasons
        // For simplicity, we'll show a generic API error here
        // In a real app, you might parse response.text() for more details
        console.error('Netlify form submission failed:', response);
        toast({
          title: newsletterStrings.toast.errorTitle,
          description: newsletterStrings.toast.apiErrorDescription || "Error al procesar la suscripción.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error submitting newsletter form to Netlify:', error);
      toast({
        title: newsletterStrings.toast.errorTitle,
        description: newsletterStrings.toast.apiErrorDescription || "Error de red al procesar la suscripción.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="newsletter-bg-image py-20 text-white">
      <div className="container mx-auto px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6 drop-shadow-lg">{newsletterStrings.title}</h2>
        <p className="text-xl mb-10 max-w-2xl mx-auto drop-shadow-sm">
          {newsletterStrings.subtitle}
        </p>

        <form
          name="newsletter"
          className="max-w-md mx-auto flex"
          onSubmit={handleSubmit}
        >
          {/* Hidden input for Netlify form name */}
          <input type="hidden" name="form-name" value="newsletter" />
          {/* Optional: Honeypot field for spam */}
          <div hidden>
            <label>
              No llenar este campo si eres humano: <input name="bot-field-newsletter" />
            </label>
          </div>

          <Input
            type="email"
            name="email"
            placeholder={newsletterStrings.emailPlaceholder}
            className="flex-grow px-4 py-3 rounded-l-full focus:outline-none text-foreground bg-card border-none ring-0 focus:ring-0 focus:ring-offset-0 h-auto"
            aria-label={newsletterStrings.emailAriaLabel}
            required
            disabled={isSubmitting}
          />
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-r-full font-bold transition h-auto"
          >
            {isSubmitting ? 'Suscribiendo...' : commonStrings.subscribe}
          </Button>
        </form>
      </div>
    </section>
  );
}
