import { PricingCard } from '@/components/common/pricing-card';
import { appStrings } from '@/helpers/strings';

export function PricingSection() {
  const pricingStrings = appStrings.pricingSection!;
  const commonStrings = appStrings.common;

  const packagesData = pricingStrings.packages.map((pkg: any) => ({
    ...pkg, // Spread existing props like price, features (which now contain text)
    buttonText: commonStrings.subscribe, // Standardize button text
    // features text will come directly from appStrings.pricingSection.packages[i].features[j].text
  }));

  return (
    <section id="precios" className="py-20 bg-card">
      <div className="container mx-auto px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-foreground mb-16">{pricingStrings.title}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {packagesData.map((pkg: any, index: number) => (
            <PricingCard
              key={index}
              title={pkg.title}
              price={pkg.price}
              priceSuffix={pkg.priceSuffix}
              features={pkg.features.map((f: any) => ({ text: f.text, included: f.included }))}
              isFeatured={pkg.isFeatured}
              buttonText={pkg.buttonText}
              headerBgClass={pkg.headerBgClass}
              borderColor={pkg.borderColor}
              textColor={pkg.textColor}
              priceColor={pkg.priceColor}
              badgeText={pkg.badgeText}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
