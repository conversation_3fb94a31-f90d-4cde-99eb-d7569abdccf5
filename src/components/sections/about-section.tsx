'use client';

import Image from 'next/image';
import { Leaf, HandHeart, Recycle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useI18n } from '@/helpers/i18n';

export function AboutSection() {
  const { strings } = useI18n();
  const aboutStrings = strings.aboutSection;
  const features = [
    { icon: <Leaf className="text-primary mr-2 h-5 w-5" />, textKey: 'natural' },
    { icon: <HandHeart className="text-primary mr-2 h-5 w-5" />, textKey: 'handmade' },
    { icon: <Recycle className="text-primary mr-2 h-5 w-5" />, textKey: 'sustainable' },
  ];

  return (
    <section id="nosotros" className="py-20 bg-secondary/50">
      <div className="container mx-auto px-6">
        <div className="flex flex-col md:flex-row items-center gap-10 md:gap-16">
          <div className="md:w-1/2">
             <div className="relative aspect-[4/3] w-full rounded-lg shadow-xl overflow-hidden bg-[#222222] text-[#aaaaaa]">
                <Image
                  src="/images/History.png"
                  alt={aboutStrings.imageAlt}
                  fill
                  className="object-cover object-bottom"
                  sizes="(max-width: 767px) 100vw, 50vw"
                />
            </div>
          </div>
          <div className="md:w-1/2">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">{aboutStrings.title}</h2>
            <p className="text-muted-foreground mb-6">
              {aboutStrings.paragraph1}
            </p>
            <p className="text-muted-foreground mb-8">
              {aboutStrings.paragraph2}
            </p>
            <div className="flex flex-wrap gap-4">
              {features.map((feature, index) => (
                <Badge key={index} variant="outline" className="bg-card px-4 py-2 text-sm shadow">
                  {feature.icon}
                  <span className="text-foreground">{aboutStrings.features[feature.textKey as keyof typeof aboutStrings.features]?.text || feature.textKey}</span>
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
