// src/components/test/SmoothI18nTest.tsx
// Componente con transiciones suaves y overlay de carga

'use client';

import React from 'react';
import { useSmoothI18nProducts } from '@/helpers/database/use-smooth-i18n';
import { useI18n } from '@/helpers/i18n';
import LoadingOverlay from '@/components/ui/LoadingOverlay';
import type { SupportedLocale } from '@/helpers/i18n/types';

export default function SmoothI18nTest() {
  const { locale, setLocale } = useI18n();
  const { 
    products, 
    tags, 
    loading, 
    error, 
    smoothLoading, 
    changeLocale 
  } = useSmoothI18nProducts();

  const handleLocaleChange = async (newLocale: string) => {
    console.log('🎨 Smooth locale change triggered:', locale, '→', newLocale);
    
    // Cambiar el locale en el contexto
    setLocale(newLocale as SupportedLocale);
    
    // Ejecutar transición suave
    await changeLocale(newLocale);
  };

  const formatPrice = (price: number, currency: string) => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price);
    } catch (error) {
      return `${currency} ${price.toFixed(2)}`;
    }
  };

  if (loading && !smoothLoading.isTransitioning) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-6"></div>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="border rounded-lg p-4">
                <div className="h-48 bg-gray-200 rounded mb-4"></div>
                <div className="h-6 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h2 className="text-red-800 font-semibold mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Overlay de carga suave */}
      <LoadingOverlay 
        isVisible={smoothLoading.isTransitioning}
        locale={smoothLoading.targetLocale || locale}
      />

      <div className="p-6 max-w-6xl mx-auto">
        {/* Header con selector de idioma */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">
            🎨 Sistema Suave de Internacionalización
          </h1>
          
          <div className="flex gap-4 mb-4 items-center">
            <div className="flex gap-2 items-center">
              <span className="font-medium">Idioma actual:</span>
              <select
                value={locale}
                onChange={(e) => handleLocaleChange(e.target.value)}
                disabled={smoothLoading.isTransitioning}
                className="border rounded px-3 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <option value="es">🇪🇸 Español</option>
                <option value="en">🇺🇸 English</option>
                <option value="pt">🇧🇷 Português</option>
                <option value="fr">🇫🇷 Français</option>
                <option value="de">🇩🇪 Deutsch</option>
              </select>
            </div>

            {smoothLoading.isTransitioning && (
              <div className="flex items-center gap-2 text-blue-600">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-sm">Cambiando idioma...</span>
              </div>
            )}
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <p><strong>✅ Productos:</strong> {products.length}</p>
            <p><strong>✅ Tags:</strong> {tags.length}</p>
            <p><strong>✅ Idioma:</strong> {locale}</p>
            <p><strong>✅ Estado:</strong> {
              smoothLoading.isTransitioning 
                ? `🔄 Transicionando (${smoothLoading.progress}%)` 
                : '✅ Listo'
            }</p>
          </div>
        </div>

        {/* Lista de productos con transición */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">📦 Productos</h2>
          <div className={`grid gap-6 md:grid-cols-2 lg:grid-cols-3 transition-opacity duration-300 ${
            smoothLoading.isTransitioning ? 'opacity-50' : 'opacity-100'
          }`}>
            {products.map((product) => (
              <div 
                key={product.id} 
                className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300"
              >
                {/* Imagen del producto */}
                {product.image_src && (
                  <img 
                    src={product.image_src} 
                    alt={product.image_alt || product.name}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                )}
                
                {/* Información del producto */}
                <h3 className="font-bold text-lg mb-2 transition-all duration-300">
                  {product.name}
                </h3>
                
                {product.description && (
                  <p className="text-gray-600 text-sm mb-3 transition-all duration-300">
                    {product.description}
                  </p>
                )}
                
                {/* Precio */}
                <div className="flex justify-between items-center mb-3">
                  <span className="text-xl font-bold text-green-600 transition-all duration-300">
                    {formatPrice(product.price, product.currency)}
                  </span>
                  <span className="text-xs text-gray-500">
                    {product.currency}
                  </span>
                </div>
                
                {/* Tags */}
                {product.tags && product.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-3">
                    {product.tags.map((tag: string, index: number) => (
                      <span 
                        key={index}
                        className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs transition-all duration-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
                
                {/* Estado de disponibilidad */}
                <div className="flex justify-between items-center text-sm">
                  <span className={`px-2 py-1 rounded text-xs transition-all duration-300 ${
                    product.availability && product.stock > 0 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {product.availability && product.stock > 0 
                      ? `✅ Disponible (${product.stock})` 
                      : '❌ Agotado'
                    }
                  </span>
                  
                  {product.size && (
                    <span className="text-gray-500">{product.size}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Lista de tags */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">🏷️ Tags Traducidos</h2>
          <div className={`flex flex-wrap gap-2 transition-opacity duration-300 ${
            smoothLoading.isTransitioning ? 'opacity-50' : 'opacity-100'
          }`}>
            {tags.map((tag) => (
              <span 
                key={tag.id}
                className="bg-gray-100 px-3 py-1 rounded-full text-sm transition-all duration-300 hover:bg-gray-200"
                title={`Original: ${tag.original_name}`}
              >
                {tag.name}
              </span>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
