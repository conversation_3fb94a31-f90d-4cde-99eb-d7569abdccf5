// src/components/test/SimpleI18nTest.tsx
// Componente de prueba simplificado para el sistema i18n

'use client';

import React, { useState } from 'react';
import { useI18n } from '@/helpers/i18n';
import type { SupportedLocale } from '@/helpers/i18n/types';

export default function SimpleI18nTest() {
  const { locale, setLocale, strings } = useI18n();
  const [testData, setTestData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Función para probar la conexión a Supabase
  const testSupabaseConnection = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔄 Testing Supabase connection...');

      // Importar dinámicamente para evitar problemas de SSR
      const { createClient } = await import('@supabase/supabase-js');

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

      console.log('🔧 Supabase config:', {
        url: supabaseUrl,
        keyLength: supabaseKey?.length
      });

      const supabase = createClient(supabaseUrl, supabaseKey);

      // Probar consulta simple
      console.log('📊 Executing test query...');
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('id, name, price')
        .limit(3);

      if (productsError) {
        console.error('❌ Supabase query error:', productsError);
        throw new Error(`Supabase error: ${productsError.message}`);
      }

      console.log('✅ Products from Supabase:', products);
      setTestData({ products, count: products?.length || 0 });

    } catch (err) {
      console.error('❌ Error testing Supabase:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const handleLocaleChange = (newLocale: string) => {
    console.log('🌍 Changing locale from', locale, 'to', newLocale);
    setLocale(newLocale as SupportedLocale);
    console.log('✅ Locale changed to:', newLocale);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">🧪 Prueba Simple de I18n</h1>

      {/* Test del sistema i18n */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">🌍 Sistema de Idiomas</h2>

        <div className="flex gap-4 items-center mb-4">
          <span className="font-medium">Idioma actual:</span>
          <select
            value={locale}
            onChange={(e) => handleLocaleChange(e.target.value)}
            className="border rounded px-3 py-2"
          >
            <option value="es">🇪🇸 Español</option>
            <option value="en">🇺🇸 English</option>
            <option value="pt">🇧🇷 Português</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="de">🇩🇪 Deutsch</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>Locale activo:</strong> {locale}
          </div>
          <div>
            <strong>Nombre del sitio:</strong> {strings.site.name}
          </div>
          <div>
            <strong>Título de navegación:</strong> {strings.nav.home}
          </div>
          <div>
            <strong>Botón de contacto:</strong> {strings.nav.contact}
          </div>
        </div>
      </div>

      {/* Test de conexión a Supabase */}
      <div className="bg-green-50 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">🗄️ Conexión a Base de Datos</h2>

        <button
          onClick={testSupabaseConnection}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? '🔄 Probando...' : '🧪 Probar Conexión'}
        </button>

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded text-red-700">
            <strong>Error:</strong> {error}
          </div>
        )}

        {testData && (
          <div className="mt-4 p-3 bg-green-100 border border-green-300 rounded">
            <strong>✅ Conexión exitosa!</strong>
            <p>Productos encontrados: {testData.count}</p>
            {testData.products && (
              <div className="mt-2">
                <strong>Productos:</strong>
                <ul className="list-disc list-inside">
                  {testData.products.map((product: any) => (
                    <li key={product.id}>
                      {product.name} - {product.price}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Información de debug */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h2 className="text-xl font-bold mb-4">🔍 Debug Info</h2>
        <pre className="text-xs bg-white p-3 rounded border overflow-auto">
          {JSON.stringify({
            locale,
            stringsAvailable: !!strings,
            siteStrings: strings.site,
            navStrings: strings.nav
          }, null, 2)}
        </pre>
      </div>
    </div>
  );
}
