// src/components/test/SimpleProductsTest.tsx
// Componente de prueba ultra-simplificado para diagnosticar el problema

'use client';

import React, { useState, useEffect } from 'react';
import { useI18n } from '@/helpers/i18n';
import { createClient } from '@supabase/supabase-js';

export default function SimpleProductsTest() {
  const { locale, setLocale } = useI18n();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('🎯 SimpleProductsTest rendered with locale:', locale);

  useEffect(() => {
    console.log('🔥 useEffect triggered for locale:', locale);
    
    const fetchProducts = async () => {
      try {
        console.log('🔄 Starting fetch...');
        setLoading(true);
        setError(null);

        // Crear cliente de Supabase directamente
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
        
        console.log('🔧 Supabase config:', { url: supabaseUrl, keyLength: supabaseKey.length });
        
        const supabase = createClient(supabaseUrl, supabaseKey);

        // Consulta simple
        console.log('📊 Executing query...');
        const { data, error: queryError } = await supabase
          .from('products')
          .select('id, name, price')
          .limit(5);

        if (queryError) {
          console.error('❌ Query error:', queryError);
          throw queryError;
        }

        console.log('✅ Query successful:', data?.length, 'products');
        setProducts(data || []);

      } catch (err) {
        console.error('❌ Fetch error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        console.log('🏁 Fetch completed');
        setLoading(false);
      }
    };

    fetchProducts();
  }, [locale]);

  const handleLocaleChange = (newLocale: string) => {
    console.log('🌍 Changing locale to:', newLocale);
    setLocale(newLocale as any);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">🧪 Prueba Ultra-Simple</h1>
      
      {/* Selector de idioma */}
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">🌍 Selector de Idioma</h2>
        
        <div className="flex gap-4 items-center mb-4">
          <span className="font-medium">Idioma actual: {locale}</span>
          <select 
            value={locale} 
            onChange={(e) => handleLocaleChange(e.target.value)}
            className="border rounded px-3 py-2"
          >
            <option value="es">🇪🇸 Español</option>
            <option value="en">🇺🇸 English</option>
            <option value="pt">🇧🇷 Português</option>
            <option value="fr">🇫🇷 Français</option>
            <option value="de">🇩🇪 Deutsch</option>
          </select>
        </div>
      </div>

      {/* Estado de carga */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-bold mb-4">📊 Estado</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div><strong>Loading:</strong> {loading ? '🔄 Sí' : '✅ No'}</div>
          <div><strong>Error:</strong> {error ? `❌ ${error}` : '✅ Ninguno'}</div>
          <div><strong>Productos:</strong> {products.length}</div>
          <div><strong>Locale:</strong> {locale}</div>
        </div>
      </div>

      {/* Resultados */}
      <div className="bg-green-50 p-4 rounded-lg">
        <h2 className="text-xl font-bold mb-4">📦 Productos</h2>
        
        {loading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2">Cargando productos...</p>
          </div>
        )}
        
        {error && (
          <div className="bg-red-100 border border-red-300 rounded p-4 text-red-700">
            <strong>Error:</strong> {error}
          </div>
        )}
        
        {!loading && !error && products.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No se encontraron productos
          </div>
        )}
        
        {!loading && !error && products.length > 0 && (
          <div className="space-y-2">
            {products.map((product) => (
              <div key={product.id} className="bg-white p-3 rounded border">
                <div className="font-medium">{product.name}</div>
                <div className="text-sm text-gray-600">{product.price}</div>
                <div className="text-xs text-gray-400">ID: {product.id}</div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
