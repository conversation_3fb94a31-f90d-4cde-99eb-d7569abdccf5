// src/components/test/I18nProductsTest.tsx
// Componente de prueba para el sistema de productos multiidioma

'use client';

import React, { useState, useEffect } from 'react';
import { useI18nProducts, useI18nTags, useFormattedPrice } from '@/helpers/database';
import { useI18n } from '@/helpers/i18n';
import type { SupportedLocale } from '@/helpers/i18n/types';

export default function I18nProductsTest() {
  const { locale, setLocale, strings } = useI18n();
  const { products, loading: productsLoading, error: productsError, refetch: refetchProducts } = useI18nProducts();
  const { tags, loading: tagsLoading, error: tagsError, refetch: refetchTags } = useI18nTags();
  const [previousLocale, setPreviousLocale] = useState(locale);

  // Detectar cambios de idioma y disparar recarga automática
  useEffect(() => {
    if (locale !== previousLocale) {
      console.log('🌍 Locale changed from', previousLocale, 'to', locale, '- triggering auto-reload');
      setPreviousLocale(locale);
      // Disparar recarga automática después de un pequeño delay
      setTimeout(() => {
        refetchProducts();
        refetchTags();
      }, 100);
    }
  }, [locale, previousLocale, refetchProducts, refetchTags]);

  // Función para cargar datos manualmente
  const handleLoadData = async () => {
    console.log('🔄 Manual data load triggered for locale:', locale);
    try {
      console.log('🚀 Starting manual data load...');
      await Promise.all([refetchProducts(), refetchTags()]);
      console.log('✅ Manual data load completed');
    } catch (error) {
      console.error('❌ Manual data load failed:', error);
    }
  };

  // Función para probar conexión directa a Supabase
  const handleTestSupabase = async () => {
    console.log('🧪 Testing direct Supabase connection...');
    try {
      // Importar dinámicamente
      const { createClient } = await import('@supabase/supabase-js');

      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

      console.log('🔧 Creating Supabase client with:', { url: supabaseUrl, keyLength: supabaseKey.length });

      const supabase = createClient(supabaseUrl, supabaseKey);

      console.log('📊 Executing test query...');
      const { data, error } = await supabase
        .from('products')
        .select('id, name, price')
        .limit(2);

      if (error) {
        console.error('❌ Supabase query error:', error);
        alert(`Error: ${error.message}`);
      } else {
        console.log('✅ Supabase query success:', data);
        alert(`¡Éxito! Encontrados ${data?.length || 0} productos`);
      }
    } catch (error) {
      console.error('❌ Supabase test failed:', error);
      alert(`Error de conexión: ${error}`);
    }
  };

  // Debug logs
  console.log('🔍 Debug Info:', {
    locale,
    productsCount: products.length,
    tagsCount: tags.length,
    productsLoading,
    tagsLoading,
    productsError,
    tagsError,
    envVars: {
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabaseKeyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length
    }
  });

  if (productsLoading || tagsLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">🔄 Cargando productos multiidioma...</h1>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (productsError || tagsError) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-red-600">❌ Error</h1>
        <p className="text-red-500">{productsError || tagsError}</p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header con selector de idioma */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">
          🌍 Sistema de Internacionalización - Prueba
        </h1>

        <div className="flex gap-4 mb-4 items-center">
          <div className="flex gap-2 items-center">
            <span className="font-medium">Idioma actual:</span>
            <select
              value={locale}
              onChange={(e) => {
                console.log('Cambiando idioma a:', e.target.value);
                setLocale(e.target.value as SupportedLocale);
              }}
              className="border rounded px-2 py-1"
            >
              <option value="es">🇪🇸 Español</option>
              <option value="en">🇺🇸 English</option>
              <option value="pt">🇧🇷 Português</option>
              <option value="fr">🇫🇷 Français</option>
              <option value="de">🇩🇪 Deutsch</option>
            </select>
          </div>

          <button
            onClick={handleLoadData}
            disabled={productsLoading || tagsLoading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {(productsLoading || tagsLoading) ? '🔄 Cargando...' : '🔄 Cargar Datos'}
          </button>

          <button
            onClick={handleTestSupabase}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            🧪 Test Supabase
          </button>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <p><strong>Productos encontrados:</strong> {products.length}</p>
          <p><strong>Tags encontrados:</strong> {tags.length}</p>
          <p><strong>Idioma activo:</strong> {locale}</p>
          <p><strong>Estado de carga:</strong> Productos: {productsLoading ? '🔄' : '✅'}, Tags: {tagsLoading ? '🔄' : '✅'}</p>
          {(productsError || tagsError) && (
            <p className="text-red-600"><strong>Errores:</strong> {productsError || tagsError}</p>
          )}
        </div>
      </div>

      {/* Lista de productos */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">📦 Productos</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>

      {/* Lista de tags */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">🏷️ Tags Traducidos</h2>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <span
              key={tag.id}
              className="bg-gray-100 px-3 py-1 rounded-full text-sm"
              title={`Original: ${tag.original_name}`}
            >
              {tag.name}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

// Componente para mostrar cada producto
function ProductCard({ product }: { product: any }) {
  const formattedPrice = useFormattedPrice(product.price, product.currency);

  return (
    <div className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
      {/* Imagen del producto */}
      {product.image_src && (
        <img
          src={product.image_src}
          alt={product.image_alt || product.name}
          className="w-full h-48 object-cover rounded-lg mb-4"
        />
      )}

      {/* Información del producto */}
      <h3 className="font-bold text-lg mb-2">{product.name}</h3>

      {product.description && (
        <p className="text-gray-600 text-sm mb-3">{product.description}</p>
      )}

      {/* Precio */}
      <div className="flex justify-between items-center mb-3">
        <span className="text-xl font-bold text-green-600">
          {formattedPrice}
        </span>
        <span className="text-xs text-gray-500">
          {product.currency}
        </span>
      </div>

      {/* Tags */}
      {product.tags && product.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {product.tags.map((tag: string, index: number) => (
            <span
              key={index}
              className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      {/* Estado de disponibilidad */}
      <div className="flex justify-between items-center text-sm">
        <span className={`px-2 py-1 rounded text-xs ${
          product.availability && product.stock > 0
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {product.availability && product.stock > 0
            ? `✅ Disponible (${product.stock})`
            : '❌ Agotado'
          }
        </span>

        {product.size && (
          <span className="text-gray-500">{product.size}</span>
        )}
      </div>
    </div>
  );
}
