// src/components/test/ReactiveI18nTest.tsx
// Componente de prueba con hooks reactivos simplificados

'use client';

import React, { useState, useEffect } from 'react';
import { useReactiveI18nProducts, useReactiveI18nTags } from '@/helpers/database/use-reactive-i18n';
import { useI18n } from '@/helpers/i18n';
import type { SupportedLocale } from '@/helpers/i18n/types';

export default function ReactiveI18nTest() {
  const { locale, setLocale } = useI18n();
  const [forceLocale, setForceLocale] = useState(locale);

  // Sincronizar el locale forzado con el locale del contexto
  useEffect(() => {
    console.log('🔄 Syncing locale from', forceLocale, 'to', locale);
    setForceLocale(locale);
  }, [locale]);

  const { products, loading: productsLoading, error: productsError } = useReactiveI18nProducts(forceLocale);
  const { tags, loading: tagsLoading, error: tagsError } = useReactiveI18nTags(forceLocale);

  console.log('🔍 ReactiveI18nTest Debug:', {
    locale,
    productsCount: products.length,
    tagsCount: tags.length,
    productsLoading,
    tagsLoading,
    productsError,
    tagsError
  });

  const handleLocaleChange = (newLocale: string) => {
    console.log('🌍 Changing locale from', locale, 'to', newLocale);
    // Pequeño delay para suavizar la transición
    setTimeout(() => {
      setLocale(newLocale as SupportedLocale);
    }, 50);
  };

  const formatPrice = (price: number, currency: string) => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price);
    } catch (error) {
      return `${currency} ${price.toFixed(2)}`;
    }
  };

  if (productsLoading || tagsLoading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4">🔄 Cargando...</h1>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  if (productsError || tagsError) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-4 text-red-600">❌ Error</h1>
        <p className="text-red-500">{productsError || tagsError}</p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* Header con selector de idioma */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">
          🚀 Sistema Reactivo de Internacionalización
        </h1>

        <div className="flex gap-4 mb-4 items-center">
          <div className="flex gap-2 items-center">
            <span className="font-medium">Idioma actual:</span>
            <select
              value={locale}
              onChange={(e) => handleLocaleChange(e.target.value)}
              className="border rounded px-3 py-2"
            >
              <option value="es">🇪🇸 Español</option>
              <option value="en">🇺🇸 English</option>
              <option value="pt">🇧🇷 Português</option>
              <option value="fr">🇫🇷 Français</option>
              <option value="de">🇩🇪 Deutsch</option>
            </select>
          </div>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <p><strong>✅ Productos encontrados:</strong> {products.length}</p>
          <p><strong>✅ Tags encontrados:</strong> {tags.length}</p>
          <p><strong>✅ Idioma activo:</strong> {locale}</p>
          <p><strong>✅ Estado:</strong> {productsLoading || tagsLoading ? '🔄 Cargando' : '✅ Listo'}</p>
        </div>
      </div>

      {/* Lista de productos */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">📦 Productos</h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {products.map((product) => (
            <div key={product.id} className="border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
              {/* Imagen del producto */}
              {product.image_src && (
                <img
                  src={product.image_src}
                  alt={product.image_alt || product.name}
                  className="w-full h-48 object-cover rounded-lg mb-4"
                />
              )}

              {/* Información del producto */}
              <h3 className="font-bold text-lg mb-2">{product.name}</h3>

              {product.description && (
                <p className="text-gray-600 text-sm mb-3">{product.description}</p>
              )}

              {/* Precio */}
              <div className="flex justify-between items-center mb-3">
                <span className="text-xl font-bold text-green-600">
                  {formatPrice(product.price, product.currency)}
                </span>
                <span className="text-xs text-gray-500">
                  {product.currency}
                </span>
              </div>

              {/* Tags */}
              {product.tags && product.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {product.tags.map((tag: string, index: number) => (
                    <span
                      key={index}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Estado de disponibilidad */}
              <div className="flex justify-between items-center text-sm">
                <span className={`px-2 py-1 rounded text-xs ${
                  product.availability && product.stock > 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {product.availability && product.stock > 0
                    ? `✅ Disponible (${product.stock})`
                    : '❌ Agotado'
                  }
                </span>

                {product.size && (
                  <span className="text-gray-500">{product.size}</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Lista de tags */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">🏷️ Tags Traducidos</h2>
        <div className="flex flex-wrap gap-2">
          {tags.map((tag) => (
            <span
              key={tag.id}
              className="bg-gray-100 px-3 py-1 rounded-full text-sm"
              title={`Original: ${tag.original_name}`}
            >
              {tag.name}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
