// src/components/ui/LoadingOverlay.tsx
// Overlay de carga elegante para cambios de idioma

'use client';

import React from 'react';

// Importar el contexto de datos i18n si está disponible
let useI18nData: any = null;
try {
  const i18nDataModule = require('@/contexts/I18nDataContext');
  useI18nData = i18nDataModule.useI18nData;
} catch (error) {
  // El contexto no está disponible
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  locale?: string;
}

// Componente que usa automáticamente el contexto global
export function GlobalLoadingOverlay() {
  const i18nDataContext = useI18nData ? useI18nData() : null;

  if (!i18nDataContext?.isTransitioning) return null;

  return (
    <LoadingOverlay
      isVisible={true}
      locale={i18nDataContext.targetLocale || 'es'}
    />
  );
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = "Cargando...",
  locale = "es"
}) => {
  if (!isVisible) return null;

  const getLoadingMessage = (locale: string) => {
    const messages = {
      es: "Cambiando idioma...",
      en: "Changing language...",
      pt: "Mudando idioma...",
      fr: "Changement de langue...",
      de: "Sprache wechseln..."
    };
    return messages[locale as keyof typeof messages] || messages.es;
  };

  const getFlag = (locale: string) => {
    const flags = {
      es: "🇪🇸",
      en: "🇺🇸",
      pt: "🇧🇷",
      fr: "🇫🇷",
      de: "🇩🇪"
    };
    return flags[locale as keyof typeof flags] || "🌍";
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white rounded-lg p-8 shadow-2xl max-w-sm w-full mx-4 text-center">
        {/* Spinner animado */}
        <div className="relative mb-6">
          <div className="w-16 h-16 mx-auto">
            {/* Círculo exterior giratorio */}
            <div className="absolute inset-0 border-4 border-blue-200 rounded-full animate-spin border-t-blue-500"></div>
            {/* Flag en el centro */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-2xl">{getFlag(locale)}</span>
            </div>
          </div>
        </div>

        {/* Mensaje de carga */}
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          {getLoadingMessage(locale)}
        </h3>

        {/* Barra de progreso animada */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{
            width: '100%',
            animation: 'loading-bar 1.5s ease-in-out infinite'
          }}></div>
        </div>

        {/* Texto adicional */}
        <p className="text-sm text-gray-600">
          {locale === 'es' && "Actualizando contenido..."}
          {locale === 'en' && "Updating content..."}
          {locale === 'pt' && "Atualizando conteúdo..."}
          {locale === 'fr' && "Mise à jour du contenu..."}
          {locale === 'de' && "Inhalt wird aktualisiert..."}
        </p>
      </div>

      <style jsx>{`
        @keyframes loading-bar {
          0% { width: 0%; }
          50% { width: 100%; }
          100% { width: 0%; }
        }
      `}</style>
    </div>
  );
};

export default LoadingOverlay;
