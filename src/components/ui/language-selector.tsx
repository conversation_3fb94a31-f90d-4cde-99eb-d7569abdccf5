"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useI18n, SUPPORTED_LOCALES, LOCALE_CONFIGS } from '@/helpers/i18n';
import { Globe } from 'lucide-react';

// Importar el contexto de datos i18n si está disponible
let useI18nData: any = null;
try {
  const i18nDataModule = require('@/contexts/I18nDataContext');
  useI18nData = i18nDataModule.useI18nData;
} catch (error) {
  // El contexto no está disponible, usar el método tradicional
}

export function LanguageSelector() {
  const { locale, setLocale } = useI18n();
  const [isOpen, setIsOpen] = useState(false);

  // Usar el contexto de datos i18n si está disponible
  const i18nDataContext = useI18nData ? useI18nData() : null;

  const currentLocaleConfig = LOCALE_CONFIGS[locale];

  const handleLocaleChange = async (newLocale: string) => {
    setIsOpen(false);

    if (i18nDataContext && i18nDataContext.changeLocale) {
      // Usar el sistema suave si está disponible
      console.log('🎨 Using smooth locale change');
      await i18nDataContext.changeLocale(newLocale);
    } else {
      // Fallback al método tradicional
      console.log('🔄 Using traditional locale change');
      setLocale(newLocale as any);
    }
  };

  const isTransitioning = i18nDataContext?.isTransitioning || false;

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 px-3 text-sm font-medium"
          aria-label="Seleccionar idioma"
          disabled={isTransitioning}
        >
          {isTransitioning ? (
            <div className="w-4 h-4 mr-2 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          ) : (
            <Globe className="h-4 w-4 mr-2" />
          )}
          <span className="hidden sm:inline-block mr-1">
            {currentLocaleConfig.flag}
          </span>
          <span className="hidden md:inline-block">
            {isTransitioning ? 'Cambiando...' : currentLocaleConfig.name}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {SUPPORTED_LOCALES.map((localeCode) => {
          const config = LOCALE_CONFIGS[localeCode];
          return (
            <DropdownMenuItem
              key={localeCode}
              onClick={() => handleLocaleChange(localeCode)}
              className={`flex items-center space-x-3 cursor-pointer ${
                locale === localeCode ? 'bg-accent' : ''
              }`}
            >
              <span className="text-lg">{config.flag}</span>
              <div className="flex flex-col">
                <span className="font-medium">{config.name}</span>
                <span className="text-xs text-muted-foreground">
                  {config.currency}
                </span>
              </div>
              {locale === localeCode && (
                <span className="ml-auto text-xs text-primary">✓</span>
              )}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
