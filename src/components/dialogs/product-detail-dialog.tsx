// src/components/dialogs/product-detail-dialog.tsx
'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CheckCircle2, XCircle, Tag, Minus, Plus, ShoppingCart } from 'lucide-react';
import type { Product } from '@/types/product';
import { appStrings } from '@/helpers/strings';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useDispatch } from 'react-redux';
import { addItemToCart } from '@/store/slices/cart-slice';
import type { AppDispatch } from '@/store/store';

interface ProductDetailDialogProps {
  product: Product | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ProductDetailDialog({ product, isOpen, onOpenChange }: ProductDetailDialogProps) {
  const catalogStrings = appStrings.catalogPage!;
  const commonStrings = appStrings.common;
  const { toast } = useToast();
  const dispatch = useDispatch<AppDispatch>();
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (isOpen && product) {
      setQuantity(product.stock > 0 ? 1 : 0);
    }
  }, [isOpen, product]);


  if (!product) {
    return null;
  }

  const isAvailableForPurchase = product.availability && product.stock > 0;
  const maxQuantity = isAvailableForPurchase ? product.stock : 0;

  const sizeTranslations: { [key: string]: string } = {
    chica: "Chica",
    mediana: "Mediana",
    grande: "Grande",
  };

  const handleIncrement = () => {
    if (isAvailableForPurchase) {
      setQuantity((prev) => Math.min(prev + 1, maxQuantity));
    }
  };

  const handleDecrement = () => {
    if (isAvailableForPurchase) {
     setQuantity((prev) => Math.max(1, prev - 1));
    }
  };

  const handleAddToCart = () => {
    if (quantity <= 0 || !isAvailableForPurchase || quantity > maxQuantity) return;

    dispatch(addItemToCart({ product, quantity }));

    toast({
      title: `${quantity} ${product.name} ${catalogStrings.addedToCartSuffix}`,
      description: `${catalogStrings.cartTotalPrefix} $${(parseFloat(product.price.replace('$', '')) * quantity).toFixed(2)}`,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[80vw] md:max-w-[70vw] lg:max-w-[60vw] xl:max-w-[900px] p-0 max-h-[90vh] flex flex-col">
        <div className="grid md:grid-cols-2 gap-0 flex-grow overflow-hidden">
          {/* Left: Carousel */}
          <div className="p-6 border-r flex items-center justify-center bg-card">
            <Carousel className="w-full max-w-md aspect-square">
              <CarouselContent>
                {product.gallery && product.gallery.length > 0 ? (
                  product.gallery.map((imgSrc, index) => (
                    <CarouselItem key={index}>
                      <div className="aspect-square relative w-full h-full bg-[#222222] text-[#aaaaaa]">
                        <Image
                          src={imgSrc}
                          alt={`${product.name} - Imagen ${index + 1}`}
                          fill
                          className="object-contain rounded-md"
                          sizes="(max-width: 768px) 80vw, (max-width: 1200px) 40vw, 450px"
                        />
                      </div>
                    </CarouselItem>
                  ))
                ) : (
                  <CarouselItem>
                     <div className="aspect-square relative w-full h-full bg-[#222222] text-[#aaaaaa]">
                        <Image
                            src={product.imageSrc}
                            alt={product.imageAlt}
                            fill
                            className="object-contain rounded-md"
                            sizes="(max-width: 768px) 80vw, (max-width: 1200px) 40vw, 450px"
                        />
                        </div>
                  </CarouselItem>
                )}
              </CarouselContent>
              {(product.gallery && product.gallery.length > 1) && (
                <>
                  <CarouselPrevious className="absolute left-2 top-1/2 -translate-y-1/2" />
                  <CarouselNext className="absolute right-2 top-1/2 -translate-y-1/2" />
                </>
              )}
            </Carousel>
          </div>

          {/* Right: Details */}
          <ScrollArea className="h-full">
          <div className="p-6 flex flex-col h-full">
            <DialogHeader className="mb-4">
              <DialogTitle className="text-2xl md:text-3xl font-bold text-foreground">{product.name}</DialogTitle>
            </DialogHeader>

            <p className="text-2xl font-semibold text-primary mb-3">{product.price}</p>

            <div className="flex items-center text-sm mb-1">
              {isAvailableForPurchase ? (
                <CheckCircle2 className="h-4 w-4 mr-1.5 text-green-500 flex-shrink-0" />
              ) : (
                <XCircle className="h-4 w-4 mr-1.5 text-red-500 flex-shrink-0" />
              )}
              <span className={cn("font-medium", isAvailableForPurchase ? "text-green-600" : "text-red-600")}>
                {isAvailableForPurchase ? catalogStrings.availability.available : catalogStrings.availability.unavailable}
              </span>
               {isAvailableForPurchase && product.stock < 5 && product.stock > 0 && (
                <span className="ml-2 text-amber-600 font-medium">(¡Últimos {product.stock}!)</span>
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-3">
              {catalogStrings.sizeLabel} <span className="font-medium text-foreground">{sizeTranslations[product.size.toLowerCase()] || product.size}</span>
            </p>

            {(Array.isArray(product.tags) ? product.tags : []).length > 0 && (
              <div className="flex flex-wrap gap-1.5 items-center text-sm mb-4">
                <Tag className="h-4 w-4 text-muted-foreground mr-0.5" />
                {(Array.isArray(product.tags) ? product.tags : []).slice(0, 5).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="font-normal text-xs">
                    {String(tag)}
                  </Badge>
                ))}
              </div>
            )}

            <p className="text-muted-foreground mb-4 text-sm">{product.description}</p>

            <div className="mb-6 flex-grow">
                <h3 className="text-md font-semibold text-foreground mb-2">Descripción Detallada</h3>
                <ScrollArea className="h-[120px] md:h-[150px] pr-3">
                    <p className="text-sm text-muted-foreground whitespace-pre-wrap leading-relaxed">
                    {product.longDescription}
                    </p>
                </ScrollArea>
            </div>


            <div className="mt-auto space-y-4 pt-4 border-t">
               <div className="space-y-1.5">
                <label htmlFor={`dialog-quantity-${product.id}`} className="text-sm text-muted-foreground font-medium">
                    {catalogStrings.quantityLabel}
                </label>
                <div className="flex items-center space-x-2">
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={handleDecrement}
                        className="h-9 w-9 shrink-0 rounded-full"
                        disabled={!isAvailableForPurchase}
                    >
                        <Minus className="h-4 w-4" />
                    </Button>
                    <Input
                      id={`dialog-quantity-${product.id}`}
                      type="text"
                      value={quantity}
                      readOnly
                      className="h-9 w-16 text-center bg-background focus-visible:ring-primary focus-visible:ring-1 focus-visible:ring-offset-0"
                      aria-label={catalogStrings.quantityLabel}
                      disabled={!isAvailableForPurchase}
                    />
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={handleIncrement}
                        className="h-9 w-9 shrink-0 rounded-full"
                        disabled={!isAvailableForPurchase || quantity >= maxQuantity}
                    >
                        <Plus className="h-4 w-4" />
                    </Button>
                </div>
              </div>
              <Button
                onClick={handleAddToCart}
                className="w-full h-11 text-base rounded-full"
                size="lg"
                disabled={quantity === 0 || !isAvailableForPurchase || quantity > maxQuantity}
              >
                <ShoppingCart className="mr-2 h-5 w-5" />
                {commonStrings.addToCart}
              </Button>
            </div>
          </div>
          </ScrollArea>
        </div>

        <DialogFooter className="px-6 py-3 border-t bg-muted/20 text-center md:text-left">
          <p className="text-xs text-muted-foreground leading-relaxed">
            {catalogStrings.shippingClauses}
          </p>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
