"use client";

import { FormEvent, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { appStrings } from '@/helpers/strings';
import { useToast } from '@/hooks/use-toast';

export function ContactForm() {
  const contactStrings = appStrings.contactSection;
  const commonStrings = appStrings.common;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    const form = event.currentTarget;
    const formData = new FormData(form);
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const message = formData.get('message') as string;

    // Validación básica
    if (!name || name.trim().length < 2) {
      toast({
        title: "Error",
        description: "Por favor, ingresa tu nombre (mínimo 2 caracteres).",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    if (!email || !email.includes('@')) {
      toast({
        title: "Error",
        description: "Por favor, ingresa un email válido.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    if (!message || message.trim().length < 10) {
      toast({
        title: "Error",
        description: "Por favor, ingresa un mensaje (mínimo 10 caracteres).",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    try {
      // Submit to the current page for Netlify Forms processing
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(formData as any).toString(),
      });

      if (response.ok) {
        toast({
          title: contactStrings.toast?.successTitle || "Mensaje enviado",
          description: contactStrings.toast?.successDescription || "Gracias por contactarnos. Te responderemos pronto.",
        });
        form.reset();
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      toast({
        title: "Error",
        description: contactStrings.toast?.apiErrorDescription || "Hubo un problema al enviar tu mensaje. Por favor, inténtalo más tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form
      name="contact"
      onSubmit={handleSubmit}
      className="bg-background/70 p-8 rounded-lg shadow-md"
    >
      <input type="hidden" name="form-name" value="contact" />

      {/* Campo Nombre */}
      <div className="mb-4">
        <label htmlFor="name" className="block text-foreground font-medium mb-2">
          {contactStrings.form.nameLabel}
        </label>
        <Input
          type="text"
          id="name"
          name="name"
          required
          disabled={isSubmitting}
          className="w-full px-4 py-2 rounded-lg border-border focus:outline-none focus:ring-2 focus:ring-primary bg-card"
          placeholder="Tu nombre completo"
        />
      </div>

      {/* Campo Email */}
      <div className="mb-4">
        <label htmlFor="email" className="block text-foreground font-medium mb-2">
          {contactStrings.form.emailLabel}
        </label>
        <Input
          type="email"
          id="email"
          name="email"
          required
          disabled={isSubmitting}
          className="w-full px-4 py-2 rounded-lg border-border focus:outline-none focus:ring-2 focus:ring-primary bg-card"
          placeholder="<EMAIL>"
        />
      </div>

      {/* Campo Mensaje */}
      <div className="mb-6">
        <label htmlFor="message" className="block text-foreground font-medium mb-2">
          {contactStrings.form.messageLabel}
        </label>
        <Textarea
          id="message"
          name="message"
          required
          disabled={isSubmitting}
          rows={4}
          className="w-full px-4 py-2 rounded-lg border-border focus:outline-none focus:ring-2 focus:ring-primary bg-card resize-none"
          placeholder="Cuéntanos en qué podemos ayudarte..."
        />
      </div>

      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-bold py-3 px-4 rounded-full transition"
      >
        {isSubmitting ? 'Enviando...' : commonStrings.sendMessage}
      </Button>
    </form>
  );
}
