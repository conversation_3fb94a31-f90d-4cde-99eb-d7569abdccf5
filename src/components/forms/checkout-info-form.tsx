"use client";

import { useState, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useI18n } from '@/helpers/i18n';
import { useToast } from '@/hooks/use-toast';

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  company: string;
  shippingAddress: string;
  shippingAddress2: string;
  shippingCity: string;
  shippingState: string;
  shippingPostalCode: string;
  shippingCountry: string;
  sameAsShipping: boolean;
  billingAddress: string;
  billingAddress2: string;
  billingCity: string;
  billingState: string;
  billingPostalCode: string;
  billingCountry: string;
}

interface CheckoutInfoFormProps {
  onInfoSubmitted: (info: CustomerInfo) => void;
  cartItems: any[];
  orderTotal: number;
}

export function CheckoutInfoForm({ onInfoSubmitted, cartItems, orderTotal }: CheckoutInfoFormProps) {
  const { strings: appStrings, localeConfig } = useI18n();
  const checkoutStrings = appStrings.checkoutPage;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sameAsShipping, setSameAsShipping] = useState(true);

  const [formData, setFormData] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    company: '',
    shippingAddress: '',
    shippingAddress2: '',
    shippingCity: '',
    shippingState: '',
    shippingPostalCode: '',
    shippingCountry: localeConfig.defaultCountry,
    sameAsShipping: true,
    billingAddress: '',
    billingAddress2: '',
    billingCity: '',
    billingState: '',
    billingPostalCode: '',
    billingCountry: localeConfig.defaultCountry,
  });

  const handleInputChange = (field: keyof CustomerInfo, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): { isValid: boolean; errorMessage: string } => {
    // Always validate shipping address fields (required for delivery)
    const shippingRequired = [
      { field: 'firstName', message: checkoutStrings.validation.firstNameRequired },
      { field: 'lastName', message: checkoutStrings.validation.lastNameRequired },
      { field: 'email', message: checkoutStrings.validation.emailRequired },
      { field: 'phone', message: checkoutStrings.validation.phoneRequired },
      { field: 'shippingAddress', message: checkoutStrings.validation.shippingAddressRequired },
      { field: 'shippingCity', message: checkoutStrings.validation.shippingCityRequired },
      { field: 'shippingState', message: checkoutStrings.validation.shippingStateRequired },
      { field: 'shippingPostalCode', message: checkoutStrings.validation.shippingPostalCodeRequired },
      { field: 'shippingCountry', message: checkoutStrings.validation.shippingCountryRequired },
    ];

    // Check shipping/customer info (always required)
    for (const { field, message } of shippingRequired) {
      const value = formData[field as keyof CustomerInfo] as string;
      if (!value || value.trim() === '') {
        return { isValid: false, errorMessage: message };
      }
    }

    // Validate email format
    if (!formData.email.includes('@')) {
      return { isValid: false, errorMessage: checkoutStrings.validation.emailInvalid };
    }

    // Only validate billing address if user unchecked "same as shipping"
    if (!sameAsShipping) {
      const billingRequired = [
        { field: 'billingAddress', message: checkoutStrings.validation.billingAddressRequired },
        { field: 'billingCity', message: checkoutStrings.validation.billingCityRequired },
        { field: 'billingState', message: checkoutStrings.validation.billingStateRequired },
        { field: 'billingPostalCode', message: checkoutStrings.validation.billingPostalCodeRequired },
        { field: 'billingCountry', message: checkoutStrings.validation.billingCountryRequired },
      ];

      for (const { field, message } of billingRequired) {
        const value = formData[field as keyof CustomerInfo] as string;
        if (!value || value.trim() === '') {
          return { isValid: false, errorMessage: message };
        }
      }
    }

    return { isValid: true, errorMessage: '' };
  };

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsSubmitting(true);

    const validation = validateForm();
    if (!validation.isValid) {
      toast({
        title: checkoutStrings.toast.validationErrorTitle,
        description: validation.errorMessage,
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    try {
      // Prepare form data for Netlify
      const netlifyFormData = new FormData();
      netlifyFormData.append('form-name', 'checkout-info');

      // Customer info
      netlifyFormData.append('firstName', formData.firstName);
      netlifyFormData.append('lastName', formData.lastName);
      netlifyFormData.append('email', formData.email);
      netlifyFormData.append('phone', formData.phone);
      netlifyFormData.append('company', formData.company);

      // Shipping address
      netlifyFormData.append('shippingAddress', formData.shippingAddress);
      netlifyFormData.append('shippingAddress2', formData.shippingAddress2);
      netlifyFormData.append('shippingCity', formData.shippingCity);
      netlifyFormData.append('shippingState', formData.shippingState);
      netlifyFormData.append('shippingPostalCode', formData.shippingPostalCode);
      netlifyFormData.append('shippingCountry', formData.shippingCountry);

      // Billing address
      netlifyFormData.append('sameAsShipping', sameAsShipping ? 'true' : 'false');
      if (!sameAsShipping) {
        netlifyFormData.append('billingAddress', formData.billingAddress);
        netlifyFormData.append('billingAddress2', formData.billingAddress2);
        netlifyFormData.append('billingCity', formData.billingCity);
        netlifyFormData.append('billingState', formData.billingState);
        netlifyFormData.append('billingPostalCode', formData.billingPostalCode);
        netlifyFormData.append('billingCountry', formData.billingCountry);
      }

      // Order info
      const orderItemsText = cartItems.map(item =>
        `${item.product.name} (x${item.quantity}) - ${item.product.price}`
      ).join('\n');
      netlifyFormData.append('orderItems', orderItemsText);
      netlifyFormData.append('orderTotal', `$${orderTotal.toFixed(2)}`);

      // Submit to Netlify
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(netlifyFormData as any).toString(),
      });

      if (response.ok) {
        toast({
          title: checkoutStrings.toast.infoSavedTitle,
          description: checkoutStrings.toast.infoSavedDescription,
        });

        // Pass the info to parent component
        const finalInfo = sameAsShipping ? {
          ...formData,
          billingAddress: formData.shippingAddress,
          billingAddress2: formData.shippingAddress2,
          billingCity: formData.shippingCity,
          billingState: formData.shippingState,
          billingPostalCode: formData.shippingPostalCode,
          billingCountry: formData.shippingCountry,
        } : formData;

        onInfoSubmitted(finalInfo);
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Error submitting checkout info:', error);
      toast({
        title: "Error",
        description: "Hubo un problema al guardar tu información. Por favor, inténtalo más tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form name="checkout-info" onSubmit={handleSubmit} className="space-y-6">
      <input type="hidden" name="form-name" value="checkout-info" />

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle>{checkoutStrings.customerInfo}</CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            {checkoutStrings.validation.shippingInfoNote}
          </p>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.firstName} *
            </label>
            <Input
              id="firstName"
              name="firstName"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              placeholder={checkoutStrings.form.firstNamePlaceholder}
            />
          </div>

          <div>
            <label htmlFor="lastName" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.lastName} *
            </label>
            <Input
              id="lastName"
              name="lastName"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              placeholder={checkoutStrings.form.lastNamePlaceholder}
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.email} *
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              required
              disabled={isSubmitting}
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder={checkoutStrings.form.emailPlaceholder}
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.phone} *
            </label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              required
              disabled={isSubmitting}
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder={checkoutStrings.form.phonePlaceholder}
            />
          </div>

          <div className="md:col-span-2">
            <label htmlFor="company" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.company}
            </label>
            <Input
              id="company"
              name="company"
              type="text"
              disabled={isSubmitting}
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              placeholder={checkoutStrings.form.companyPlaceholder}
            />
          </div>
        </CardContent>
      </Card>

      {/* Shipping Address */}
      <Card>
        <CardHeader>
          <CardTitle>{checkoutStrings.shippingAddress}</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="md:col-span-2">
            <label htmlFor="shippingAddress" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.address} *
            </label>
            <Input
              id="shippingAddress"
              name="shippingAddress"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.shippingAddress}
              onChange={(e) => handleInputChange('shippingAddress', e.target.value)}
              placeholder={checkoutStrings.form.addressPlaceholder}
            />
          </div>

          <div className="md:col-span-2">
            <label htmlFor="shippingAddress2" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.address2}
            </label>
            <Input
              id="shippingAddress2"
              name="shippingAddress2"
              type="text"
              disabled={isSubmitting}
              value={formData.shippingAddress2}
              onChange={(e) => handleInputChange('shippingAddress2', e.target.value)}
              placeholder={checkoutStrings.form.address2Placeholder}
            />
          </div>

          <div>
            <label htmlFor="shippingCity" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.city} *
            </label>
            <Input
              id="shippingCity"
              name="shippingCity"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.shippingCity}
              onChange={(e) => handleInputChange('shippingCity', e.target.value)}
              placeholder={checkoutStrings.form.cityPlaceholder}
            />
          </div>

          <div>
            <label htmlFor="shippingState" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.state} *
            </label>
            <Input
              id="shippingState"
              name="shippingState"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.shippingState}
              onChange={(e) => handleInputChange('shippingState', e.target.value)}
              placeholder={checkoutStrings.form.statePlaceholder}
            />
          </div>

          <div>
            <label htmlFor="shippingPostalCode" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.postalCode} *
            </label>
            <Input
              id="shippingPostalCode"
              name="shippingPostalCode"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.shippingPostalCode}
              onChange={(e) => handleInputChange('shippingPostalCode', e.target.value)}
              placeholder={checkoutStrings.form.postalCodePlaceholder}
            />
          </div>

          <div>
            <label htmlFor="shippingCountry" className="block text-sm font-medium mb-2">
              {checkoutStrings.form.country} *
            </label>
            <Input
              id="shippingCountry"
              name="shippingCountry"
              type="text"
              required
              disabled={isSubmitting}
              value={formData.shippingCountry}
              onChange={(e) => handleInputChange('shippingCountry', e.target.value)}
              placeholder={checkoutStrings.form.countryPlaceholder}
            />
          </div>
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle>{checkoutStrings.billingAddress}</CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            {checkoutStrings.validation.billingInfoNote}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sameAsShipping"
              checked={sameAsShipping}
              onCheckedChange={(checked) => {
                setSameAsShipping(checked as boolean);
                handleInputChange('sameAsShipping', checked as boolean);
              }}
            />
            <label htmlFor="sameAsShipping" className="text-sm font-medium">
              {checkoutStrings.sameAsShipping}
            </label>
          </div>

          {!sameAsShipping && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="billingAddress" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.address} {!sameAsShipping ? '*' : ''}
                </label>
                <Input
                  id="billingAddress"
                  name="billingAddress"
                  type="text"
                  required={!sameAsShipping}
                  disabled={isSubmitting}
                  value={formData.billingAddress}
                  onChange={(e) => handleInputChange('billingAddress', e.target.value)}
                  placeholder={checkoutStrings.form.addressPlaceholder}
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="billingAddress2" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.address2}
                </label>
                <Input
                  id="billingAddress2"
                  name="billingAddress2"
                  type="text"
                  disabled={isSubmitting}
                  value={formData.billingAddress2}
                  onChange={(e) => handleInputChange('billingAddress2', e.target.value)}
                  placeholder={checkoutStrings.form.address2Placeholder}
                />
              </div>

              <div>
                <label htmlFor="billingCity" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.city} {!sameAsShipping ? '*' : ''}
                </label>
                <Input
                  id="billingCity"
                  name="billingCity"
                  type="text"
                  required={!sameAsShipping}
                  disabled={isSubmitting}
                  value={formData.billingCity}
                  onChange={(e) => handleInputChange('billingCity', e.target.value)}
                  placeholder={checkoutStrings.form.cityPlaceholder}
                />
              </div>

              <div>
                <label htmlFor="billingState" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.state} {!sameAsShipping ? '*' : ''}
                </label>
                <Input
                  id="billingState"
                  name="billingState"
                  type="text"
                  required={!sameAsShipping}
                  disabled={isSubmitting}
                  value={formData.billingState}
                  onChange={(e) => handleInputChange('billingState', e.target.value)}
                  placeholder={checkoutStrings.form.statePlaceholder}
                />
              </div>

              <div>
                <label htmlFor="billingPostalCode" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.postalCode} {!sameAsShipping ? '*' : ''}
                </label>
                <Input
                  id="billingPostalCode"
                  name="billingPostalCode"
                  type="text"
                  required={!sameAsShipping}
                  disabled={isSubmitting}
                  value={formData.billingPostalCode}
                  onChange={(e) => handleInputChange('billingPostalCode', e.target.value)}
                  placeholder={checkoutStrings.form.postalCodePlaceholder}
                />
              </div>

              <div>
                <label htmlFor="billingCountry" className="block text-sm font-medium mb-2">
                  {checkoutStrings.form.country} {!sameAsShipping ? '*' : ''}
                </label>
                <Input
                  id="billingCountry"
                  name="billingCountry"
                  type="text"
                  required={!sameAsShipping}
                  disabled={isSubmitting}
                  value={formData.billingCountry}
                  onChange={(e) => handleInputChange('billingCountry', e.target.value)}
                  placeholder={checkoutStrings.form.countryPlaceholder}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full"
        size="lg"
      >
        {isSubmitting ? 'Guardando información...' : checkoutStrings.proceedToPaymentButton}
      </Button>
    </form>
  );
}
