// src/store/slices/cart-slice.ts
import type { PayloadAction } from '@reduxjs/toolkit';
import { createSlice } from '@reduxjs/toolkit';
import type { Product } from '@/types/product';

export interface CartItem { 
  product: Product;
  quantity: number;
}

export interface CartState { 
  items: CartItem[];
}

const initialState: CartState = {
  items: [],
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addItemToCart: (state, action: PayloadAction<{ product: Product; quantity: number }>) => {
      const { product, quantity } = action.payload;
      if (quantity <= 0) return;

      const existingItemIndex = state.items.findIndex((item) => item.product.id === product.id);

      if (existingItemIndex >= 0) {
        const currentQuantity = state.items[existingItemIndex].quantity;
        const newQuantity = Math.min(currentQuantity + quantity, product.stock);
        state.items[existingItemIndex].quantity = newQuantity;
      } else {
        state.items.push({ product, quantity: Math.min(quantity, product.stock) });
      }
    },
    removeItemFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter((item) => item.product.id !== action.payload);
    },
    updateItemQuantity: (state, action: PayloadAction<{ productId: string; quantity: number }>) => {
      const { productId, quantity } = action.payload;
      const existingItemIndex = state.items.findIndex((item) => item.product.id === productId);

      if (existingItemIndex >= 0) {
        if (quantity > 0) {
          const productStock = state.items[existingItemIndex].product.stock;
          state.items[existingItemIndex].quantity = Math.min(quantity, productStock);
        } else {
          state.items.splice(existingItemIndex, 1); 
        }
      }
    },
    clearCart: (state) => {
      state.items = [];
    },
  },
});

export const { addItemToCart, removeItemFromCart, updateItemQuantity, clearCart } = cartSlice.actions;

export const selectCartItems = (cartState?: CartState): CartItem[] => {
  if (cartState) {
    if (Array.isArray(cartState.items)) {
      return cartState.items;
    }
  }
  return [];
};

export const selectTotalCartQuantity = (cartState?: CartState): number => {
  if (cartState) {
    if (Array.isArray(cartState.items)) {
      return cartState.items.reduce((total, item) => {
        const quantity = item && typeof item.quantity === 'number' ? item.quantity : 0;
        return total + quantity;
      }, 0);
    }
  }
  return 0;
};

export const selectCartTotalPrice = (cartState?: CartState): number => {
  if (cartState) {
    if (Array.isArray(cartState.items)) {
      return cartState.items.reduce((total, item) => {
        const priceString = item?.product?.price || '$0';
        const quantityValue = item?.quantity || 0;
        const price = parseFloat(priceString.replace('$', '')) || 0;
        return total + (price * quantityValue);
      }, 0);
    }
  }
  return 0;
};

export default cartSlice.reducer;
