// src/store/slices/checkout-slice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import type { CartItem } from './cart-slice';
import type { RootState } from '../store';

// Constants for default values
const DEFAULT_SHIPPING_COST = 150.00;
const DEFAULT_TAX_RATE = 0.15; // 15%

// --- Async Thunks ---
export const fetchShippingCost = createAsyncThunk<number, CartItem[]>(
  'checkout/fetchShippingCost',
  async (cartItems, thunkAPI) => {
    const apiUrl = process.env.NEXT_PUBLIC_SHIPPING_API_URL;
    if (!apiUrl) {
      console.warn('Shipping API URL (NEXT_PUBLIC_SHIPPING_API_URL) not configured in .env.local. Using default shipping cost.');
      return DEFAULT_SHIPPING_COST;
    }
    try {
      // Simulate API call that might need cartItems
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items: cartItems, totalWeight: cartItems.reduce((acc, item) => acc + item.quantity, 0) }), // Example payload
      });
      if (!response.ok) {
        // Simulate API error or not found
        console.error(`Failed to fetch shipping cost: ${response.status}. Using default.`);
        return DEFAULT_SHIPPING_COST;
      }
      const data = await response.json();
      if (typeof data.cost !== 'number') {
        console.error('Invalid shipping cost format from API. Using default.');
        return DEFAULT_SHIPPING_COST;
      }
      return data.cost;
    } catch (error) {
      // Simulate network error
      console.error('Error fetching shipping cost:', error, '. Using default.');
      return DEFAULT_SHIPPING_COST;
    }
  }
);

export const fetchTaxes = createAsyncThunk<number, { subtotal: number; items: CartItem[] }>(
  'checkout/fetchTaxes',
  async ({ subtotal, items }, thunkAPI) => {
    const apiUrl = process.env.NEXT_PUBLIC_TAX_API_URL;
    if (!apiUrl) {
      console.warn('Tax API URL (NEXT_PUBLIC_TAX_API_URL) not configured in .env.local. Calculating taxes with default rate.');
      return subtotal * DEFAULT_TAX_RATE;
    }
    try {
      // Simulate API call
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subtotal, items, destination: 'MX' }), // Example payload
      });
      if (!response.ok) {
        // Simulate API error or not found
        console.error(`Failed to fetch taxes: ${response.status}. Calculating with default rate.`);
        return subtotal * DEFAULT_TAX_RATE;
      }
      const data = await response.json();
      if (typeof data.taxAmount !== 'number') {
         console.error('Invalid tax amount format from API. Calculating with default rate.');
         return subtotal * DEFAULT_TAX_RATE;
      }
      return data.taxAmount;
    } catch (error) {
      // Simulate network error
      console.error('Error fetching taxes:', error, '. Calculating with default rate.');
      return subtotal * DEFAULT_TAX_RATE;
    }
  }
);

// --- Slice Definition ---
interface CheckoutState {
  shippingCost: number;
  taxAmount: number;
  shippingStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
  taxStatus: 'idle' | 'loading' | 'succeeded' | 'failed';
}

const initialState: CheckoutState = {
  shippingCost: DEFAULT_SHIPPING_COST, // Initialize with default, will be fetched
  taxAmount: 0, // Initialize with 0, will be calculated/fetched
  shippingStatus: 'idle',
  taxStatus: 'idle',
};

const checkoutSlice = createSlice({
  name: 'checkout',
  initialState,
  reducers: {
    resetCheckoutState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      // Shipping Cost
      .addCase(fetchShippingCost.pending, (state) => {
        state.shippingStatus = 'loading';
      })
      .addCase(fetchShippingCost.fulfilled, (state, action: PayloadAction<number>) => {
        state.shippingStatus = 'succeeded';
        state.shippingCost = action.payload;
      })
      .addCase(fetchShippingCost.rejected, (state) => {
        state.shippingStatus = 'failed';
        state.shippingCost = DEFAULT_SHIPPING_COST; // Ensure default on unexpected thunk rejection
      })
      // Taxes
      .addCase(fetchTaxes.pending, (state) => {
        state.taxStatus = 'loading';
      })
      .addCase(fetchTaxes.fulfilled, (state, action: PayloadAction<number>) => {
        state.taxStatus = 'succeeded';
        state.taxAmount = action.payload;
      })
      .addCase(fetchTaxes.rejected, (state, action) => {
        state.taxStatus = 'failed';
        // The thunk is designed to return the default calculated amount on failure,
        // so if it fulfills, action.payload will have that default.
        // If createAsyncThunk itself rejects for some other reason, we might not have subtotal here.
        // For safety, we can reset to 0 or try to recalculate if subtotal was passed in meta,
        // but the thunk's design to return default is preferred.
        // If action.meta.arg contains subtotal, we could use it:
        // const subtotal = (action.meta.arg as { subtotal: number })?.subtotal;
        // if (typeof subtotal === 'number') {
        //   state.taxAmount = subtotal * DEFAULT_TAX_RATE;
        // } else {
        //   state.taxAmount = 0; // Fallback if subtotal not available
        // }
        // However, since our thunk already returns the default value on error,
        // this .rejected case might only be hit for unhandled promise rejections within createAsyncThunk itself.
        // We will rely on the thunk to return a sensible default or the fetched value.
        // If it truly rejects, we'll ensure taxAmount is reset or uses a placeholder logic.
        // For now, let's assume the thunk handles returning default on error.
        // If the thunk were to truly reject, state.taxAmount would not be updated by `fulfilled`
        // and might need explicit setting to a default here based on available info.
        // Given thunk design, this case might be for catastrophic errors.
        // If fetchTaxes is guaranteed to resolve (even with a default), this might not be strictly needed
        // to set a default taxAmount, as 'fulfilled' will handle it.
      });
  },
});

export const { resetCheckoutState } = checkoutSlice.actions;

// Selectors
export const selectShippingCostValue = (state: RootState) => state.checkout.shippingCost;
export const selectTaxAmountValue = (state: RootState) => state.checkout.taxAmount;
export const selectShippingStatus = (state: RootState) => state.checkout.shippingStatus;
export const selectTaxStatus = (state: RootState) => state.checkout.taxStatus;

export default checkoutSlice.reducer;
