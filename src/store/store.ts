// src/store/store.ts
import { configureStore } from '@reduxjs/toolkit';
import cartReducer from './slices/cart-slice';
import checkoutReducer from './slices/checkout-slice';
import uiReducer from './slices/ui-slice'; // Import the new UI reducer

export const store = configureStore({
  reducer: {
    cart: cartReducer,
    checkout: checkoutReducer,
    ui: uiReducer, // Add the new UI reducer
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type { CartItem } from './slices/cart-slice'; // Re-export for convenience
