// src/ai/flows/payment-flows.ts
// IMPORTANT: This file now contains client-side stubs for static export compatibility.
// It does NOT use the Genkit runtime or interact with actual payment gateways.

import { z } from 'zod'; // Using Zod directly for schema definition

// --- Stripe Payment Intent Simulation ---
const StripePaymentIntentInputSchema = z.object({
  amount: z.number().describe('The amount for the payment intent in cents.'),
  currency: z.string().default('mxn').describe('The currency for the payment intent.'),
});
export type StripePaymentIntentInput = z.infer<typeof StripePaymentIntentInputSchema>;

const StripePaymentIntentOutputSchema = z.object({
  clientSecret: z.string().describe('A mock client secret for Stripe PaymentIntent.'),
  status: z.string().describe('The status of the mock operation, e.g., "succeeded_stub".')
});
export type StripePaymentIntentOutput = z.infer<typeof StripePaymentIntentOutputSchema>;

export async function createStripePaymentIntent(input: StripePaymentIntentInput): Promise<StripePaymentIntentOutput> {
  console.log('[Payment Flow Stub] Simulating Stripe Payment Intent creation with input:', input);
  // In a real application with a backend, you would make an API call here.
  // For static export, we return a mock client secret directly.
  return {
    clientSecret: `pi_${Date.now()}_secret_stub_${Math.random().toString(36).substring(7)}`,
    status: 'succeeded_stub'
  };
}

// --- PayPal Order Capture Simulation ---
const PaypalOrderCaptureInputSchema = z.object({
  orderID: z.string().describe('The PayPal Order ID to capture.'),
});
export type PaypalOrderCaptureInput = z.infer<typeof PaypalOrderCaptureInputSchema>;

const PaypalOrderCaptureOutputSchema = z.object({
  status: z.string().describe('The status of the mock PayPal order capture, e.g., "COMPLETED_STUB".'),
  transactionId: z.string().optional().describe('A mock transaction ID.'),
});
export type PaypalOrderCaptureOutput = z.infer<typeof PaypalOrderCaptureOutputSchema>;

export async function capturePaypalOrder(input: PaypalOrderCaptureInput): Promise<PaypalOrderCaptureOutput> {
  console.log('[Payment Flow Stub] Simulating PayPal Order capture for Order ID:', input.orderID);
  // In a real application with a backend, you would make an API call here.
  // For static export, we return a mock success status.
  return {
    status: 'COMPLETED_STUB',
    transactionId: `paypal_txn_stub_${Date.now()}`
  };
}
