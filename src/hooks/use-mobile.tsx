
import * as React from "react"

const MOBILE_BREAKPOINT = 768

export function useIsMobile() {
  // Initialize with false, assuming non-mobile until client-side check
  const [isMobile, setIsMobile] = React.useState<boolean>(false);

  React.useEffect(() => {
    // Check if window is defined for client-side execution
    if (typeof window === 'undefined') {
        return;
    }

    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
    };

    onChange(); // Call immediately to set the correct initial client-side value
    mql.addEventListener("change", onChange);

    return () => mql.removeEventListener("change", onChange);
  }, []); // Empty dependency array ensures this runs once on mount (client-side)

  return isMobile; // Directly return the boolean state
}
