// src/helpers/i18n/locales/pt.ts

import type { AppStrings } from '../types';

export const ptStrings: AppStrings = {
  // Site-wide
  site: {
    name: "Luminara",
    meta: {
      title: "Luminara | Velas Artesanais",
      description: "Velas artesanais feitas com ceras naturais e óleos essenciais puros para criar ambientes únicos ❤️",
    },
  },

  // Navigation
  nav: {
    homeLabel: 'Início',
    productsLabel: 'Produtos',
    aboutLabel: 'Sobre',
    testimonialsLabel: 'Depoimentos',
    contactLabel: 'Contato',
    home: 'Início',
    contact: 'Contato',
    links: [
      { href: '/#inicio', labelKey: 'homeLabel' },
      { href: '/#produtos', labelKey: 'productsLabel' },
      { href: '/#sobre', labelKey: 'aboutLabel' },
      { href: '/#depoimentos', labelKey: 'testimonialsLabel' },
      { href: '/#contato', labelKey: 'contactLabel' },
    ],
    aria: {
      shoppingCart: "Carrinho de compras",
      openMenu: "Abrir menu",
      closeMenu: "Fechar menu",
      catalogTitle: "Catálogo de Velas",
    }
  },

  // Common UI elements
  common: {
    addToCart: "Adicionar",
    exploreCollection: "Explorar coleção",
    subscribe: "Inscrever-se",
    sendMessage: "Enviar mensagem",
    goBackButtonLabel: "Voltar",
    errorTitle: "Erro",
  },

  // Hero Section
  heroSection: {
    title: "Ilumine sua vida com aromas",
    subtitle: "Velas artesanais feitas com ceras naturais e óleos essenciais puros para criar ambientes únicos ❤️",
  },

  // Newsletter Section
  newsletterSection: {
    title: "Junte-se à nossa comunidade",
    subtitle: "Inscreva-se em nossa newsletter e receba 10% de desconto na sua primeira compra, além de conteúdo exclusivo e novidades.",
    emailPlaceholder: "Seu endereço de email",
    emailAriaLabel: "Endereço de email para inscrição",
    toast: {
      successTitle: "Inscrição realizada!",
      successDescription: "Obrigado por se juntar à nossa comunidade.",
      errorTitle: "Erro",
      errorDescription: "Por favor, insira um endereço de email válido.",
      apiErrorDescription: "Houve um problema ao processar sua inscrição. Tente novamente mais tarde."
    }
  },

  // Contact Section
  contactSection: {
    title: "Entre em contato",
    subtitle: "Tem dúvidas sobre nossos produtos ou precisa de ajuda com seu pedido? Estamos aqui para ajudá-lo.",
    details: [
      { text: '+55 11 99999-9999' },
      { text: '<EMAIL>' },
    ],
    socialAriaLabels: {
      facebook: 'Facebook',
      instagram: 'Instagram',
      pinterest: 'Pinterest',
    },
    form: {
      nameLabel: "Nome",
      emailLabel: "Endereço de email",
      messageLabel: "Mensagem",
    },
    toast: {
      successTitle: "Mensagem enviada",
      successDescription: "Obrigado por entrar em contato. Responderemos em breve.",
      apiErrorDescription: "Houve um problema ao enviar sua mensagem. Tente novamente mais tarde."
    }
  },

  // Checkout Page
  checkoutPage: {
    title: "Revisão do Pedido",
    yourOrder: "Seu Pedido",
    customerInfo: "Informações do Cliente",
    shippingAddress: "Endereço de Entrega",
    billingAddress: "Endereço de Cobrança",
    sameAsShipping: "Usar o mesmo endereço para cobrança",
    itemColumnHeader: "Item",
    quantityColumnHeader: "Quantidade",
    priceColumnHeader: "Preço Unitário",
    totalColumnHeader: "Total",
    unitPrice: "Preço:",
    form: {
      firstName: "Nome",
      lastName: "Sobrenome",
      email: "Endereço de email",
      phone: "Telefone",
      company: "Empresa (opcional)",
      address: "Endereço",
      address2: "Apartamento, sala, etc. (opcional)",
      city: "Cidade",
      state: "Estado",
      postalCode: "CEP",
      country: "País",
      firstNamePlaceholder: "Seu nome",
      lastNamePlaceholder: "Seu sobrenome",
      emailPlaceholder: "<EMAIL>",
      phonePlaceholder: "+55 11 99999-9999",
      companyPlaceholder: "Nome da empresa",
      addressPlaceholder: "Rua e número",
      address2Placeholder: "Apartamento, sala, etc.",
      cityPlaceholder: "Cidade",
      statePlaceholder: "Estado",
      postalCodePlaceholder: "12345-678",
      countryPlaceholder: "Brasil",
    },
    orderSummary: {
      title: "Resumo do Pedido",
      subtotal: "Subtotal:",
      shipping: "Entrega:",
      shippingEstimate: "R$ 15,00",
      taxes: "Impostos (Estimado):",
      taxesEstimate: "Calculado na próxima etapa",
      grandTotal: "Total Geral:",
      calculating: "Calculando...",
    },
    proceedToPaymentButton: "Continuar para Pagamento",
    emptyCartMessage: "Seu carrinho está vazio. Você não pode prosseguir para o checkout.",
    continueShoppingButton: "Explorar Catálogo",
    aria: {
      productImage: "Imagem do produto"
    },
    validation: {
      firstNameRequired: "O nome é necessário",
      lastNameRequired: "O sobrenome é necessário",
      emailRequired: "O endereço de email é obrigatório",
      emailInvalid: "Por favor, insira um endereço de email válido",
      phoneRequired: "O telefone é necessário para contato",
      shippingAddressRequired: "O endereço de entrega é necessário",
      shippingCityRequired: "A cidade de entrega é obrigatória",
      shippingStateRequired: "O estado de entrega é obrigatório",
      shippingPostalCodeRequired: "O CEP de entrega é necessário",
      shippingCountryRequired: "O país de entrega é obrigatório",
      billingAddressRequired: "O endereço de cobrança é necessário",
      billingCityRequired: "A cidade de cobrança é obrigatória",
      billingStateRequired: "O estado de cobrança é obrigatório",
      billingPostalCodeRequired: "O CEP de cobrança é necessário",
      billingCountryRequired: "O país de cobrança é obrigatório",
      shippingInfoNote: "Esta informação é necessária para a entrega do seu pedido",
      billingInfoNote: "Necessário apenas se diferente do endereço de entrega",
    },
    toast: {
      infoSavedTitle: "Informações salvas",
      infoSavedDescription: "Suas informações foram salvas com sucesso.",
      validationErrorTitle: "Erro de validação",
      validationErrorDescription: "Por favor, complete todos os campos obrigatórios.",
      itemRemovedTitle: "Produto removido",
      itemRemovedDescription: "O produto foi removido do seu carrinho.",
      quantityExceedsStockTitle: "Estoque insuficiente",
      quantityExceedsStockDescription: "Você pode adicionar apenas até",
    },
    constructionBanner: {
      title: "🚧 Página em Construção",
      description: "Esta funcionalidade estará disponível em breve. Obrigado pela sua paciência!",
    },
  },

  // Payment Page
  paymentPage: {
    title: "Realizar Pagamento",
    orderSummaryTitle: "Resumo do Seu Pedido",
    subtotalLabel: "Subtotal:",
    shippingLabel: "Entrega:",
    taxesLabel: "Impostos:",
    totalLabel: "Total a Pagar:",
    selectPaymentMethod: "Selecione Seu Método de Pagamento",
    payWithPayPal: "Pagar com PayPal",
    payWithCard: "Pagar com Cartão",
    cardDetails: "Detalhes do Cartão",
    payButton: "Pagar",
    processingPayment: "Processando pagamento...",
    paymentSuccessfulTitle: "Pagamento Realizado!",
    paymentSuccessfulDescription: "Seu pedido foi processado. Obrigado pela sua compra.",
    paymentFailedTitle: "Erro no Pagamento",
    paymentFailedDescription: "Não foi possível processar seu pagamento. Tente novamente ou entre em contato com o suporte.",
    paypalOrderCreationError: "Erro ao criar pedido do PayPal.",
    paypalCaptureError: "Erro ao capturar pedido do PayPal.",
    stripePaymentError: "Erro ao processar pagamento com Stripe.",
    stripeSetupError: "Erro ao configurar Stripe. Verifique suas chaves de API.",
    missingClientSecret: "Erro de configuração: client_secret do Stripe ausente.",
    paypalUnavailable: "PayPal não está disponível no momento. Tente novamente mais tarde.",
    stripeUnavailable: "Stripe não está disponível no momento. Tente novamente mais tarde."
  },

  // Catalog Page
  catalogPage: {
    title: "Catálogo",
    bannerImageAlt: "Banner do catálogo",
    searchPlaceholder: "Buscar produtos...",
    tagInputPlaceholder: "Buscar por tags (ex: Floral, Fresco, ...)",
    clearFilters: "Limpar filtros",
    checkoutButtonLabel: "Ir ao carrinho",
    cartTotalSummaryPrefix: "Total:",
    filteringByName: "Filtrando por nome:",
    filteringByTags: "Filtrando por tags:",
    noResults: "Nenhum produto encontrado",
    emptyCart: "Nenhum produto disponível",
    addedToCartSuffix: "adicionado ao carrinho",
    cartTotalPrefix: "Total do carrinho:",
    sizeLabel: "Tamanho:",
    quantityLabel: "Quantidade",
    shippingClauses: "Frete grátis em pedidos acima de R$500",
    availability: {
      available: "Disponível",
      unavailable: "Esgotado"
    },
    pagination: {
      previous: "Anterior",
      next: "Próximo",
      page: "Página",
      of: "de"
    },
    constructionBanner: {
      title: "Em construção",
      description: "Esta funcionalidade está em desenvolvimento"
    }
  },

  // Footer
  footer: {
    tagline: "Ilumine sua vida com aromas naturais",
    quickLinksTitle: "Links Rápidos",
    policyLinksTitle: "Políticas",
    paymentMethodsTitle: "Métodos de Pagamento",
    copyright: (year: number) => `© ${year} Luminara. Todos os direitos reservados.`,
    policyLinks: [
      { href: "/politica-de-privacidade", text: "Política de Privacidade" },
      { href: "/terminos-y-condiciones", text: "Termos e Condições" },
      { href: "/politica-de-envios", text: "Política de Envio" },
      { href: "/politica-de-devoluciones", text: "Política de Devolução" }
    ],
    paymentMethods: [
      { name: "visa", icon: "visa" },
      { name: "mastercard", icon: "mastercard" },
      { name: "paypal", icon: "paypal" }
    ],
    paymentMethodLabels: {
      visa: "Visa",
      mastercard: "Mastercard",
      paypal: "PayPal"
    }
  },

  // About Section
  aboutSection: {
    title: "Sobre Nós",
    content: "Criamos velas artesanais com ingredientes naturais",
    imageAlt: "Sobre nós",
    paragraph1: "Na Luminara, criamos velas artesanais únicas com ceras naturais e óleos essenciais puros.",
    paragraph2: "Cada vela é cuidadosamente elaborada para criar ambientes especiais em sua casa.",
    features: {
      natural: { text: "100% Natural" },
      handmade: { text: "Feito à Mão" },
      sustainable: { text: "Sustentável" },
      quality: { text: "Alta Qualidade" },
      artisan: { text: "Artesanal" },
      eco: { text: "Ecológico" }
    }
  },

  // Pricing Section
  pricingSection: {
    title: "Nossos Pacotes",
    packages: []
  },

  // Products Section
  productsSection: {
    title: "Nossos Produtos",
    products: []
  },

  // Testimonials Section
  testimonialsSection: {
    title: "O que nossos clientes dizem",
    testimonials: []
  },

  // Policy Pages
  returnsPage: {
    title: "Política de Devolução",
    content: ["Conteúdo da política de devolução..."]
  },

  shippingPage: {
    title: "Política de Envio",
    content: ["Conteúdo da política de envio..."]
  },

  privacyPage: {
    title: "Política de Privacidade",
    content: ["Conteúdo da política de privacidade..."]
  },

  termsPage: {
    title: "Termos e Condições",
    content: ["Conteúdo dos termos e condições..."]
  }
};
