// src/helpers/i18n/locales/index.ts

import type { SupportedLocale, AppStrings } from '../types';
import { esStrings } from './es';
import { enStrings } from './en';
import { ptStrings } from './pt';
import { frStrings } from './fr';
import { deStrings } from './de';

export const localeStrings: Record<SupportedLocale, AppStrings> = {
  es: esStrings,
  en: enStrings,
  pt: ptStrings,
  fr: frStrings,
  de: deStrings,
};

export { esStrings, enStrings, ptStrings, frStrings, deStrings };
