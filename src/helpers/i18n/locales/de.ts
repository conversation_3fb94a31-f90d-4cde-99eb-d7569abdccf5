// src/helpers/i18n/locales/de.ts

import type { AppStrings } from '../types';

export const deStrings: AppStrings = {
  // Site-wide
  site: {
    name: "Luminara",
    meta: {
      title: "Luminara | Handgemachte Kerzen",
      description: "Handgefertigte Kerzen aus natürlichen Wachsen und reinen ätherischen Ölen für einzigartige Atmosphären ❤️",
    },
  },

  // Navigation
  nav: {
    homeLabel: 'Startseite',
    productsLabel: 'Produkte',
    aboutLabel: 'Über uns',
    testimonialsLabel: 'Bewertungen',
    contactLabel: 'Kontakt',
    home: 'Startseite',
    contact: 'Kontakt',
    links: [
      { href: '/#startseite', labelKey: 'homeLabel' },
      { href: '/#produkte', labelKey: 'productsLabel' },
      { href: '/#ueber', labelKey: 'aboutLabel' },
      { href: '/#bewertungen', labelKey: 'testimonialsLabel' },
      { href: '/#kontakt', labelKey: 'contactLabel' },
    ],
    aria: {
      shoppingCart: "Einkaufswagen",
      openMenu: "Menü öffnen",
      closeMenu: "Menü schließen",
      catalogTitle: "Kerzen-Katalog",
    }
  },

  // Common UI elements
  common: {
    addToCart: "Hinzufügen",
    exploreCollection: "Kollektion erkunden",
    subscribe: "Abonnieren",
    sendMessage: "Nachricht senden",
    goBackButtonLabel: "Zurück",
    errorTitle: "Fehler",
  },

  // Hero Section
  heroSection: {
    title: "Erhellen Sie Ihr Leben mit Düften",
    subtitle: "Handgefertigte Kerzen aus natürlichen Wachsen und reinen ätherischen Ölen für einzigartige Atmosphären ❤️",
  },

  // Newsletter Section
  newsletterSection: {
    title: "Treten Sie unserer Gemeinschaft bei",
    subtitle: "Abonnieren Sie unseren Newsletter und erhalten Sie 10% Rabatt auf Ihren ersten Einkauf, plus exklusive Inhalte und Neuigkeiten.",
    emailPlaceholder: "Ihre E-Mail-Adresse",
    emailAriaLabel: "E-Mail-Adresse für Abonnement",
    toast: {
      successTitle: "Abonnement erfolgreich!",
      successDescription: "Vielen Dank, dass Sie unserer Gemeinschaft beigetreten sind.",
      errorTitle: "Fehler",
      errorDescription: "Bitte geben Sie eine gültige E-Mail-Adresse ein.",
      apiErrorDescription: "Es gab ein Problem bei der Verarbeitung Ihres Abonnements. Bitte versuchen Sie es später erneut."
    }
  },

  // Contact Section
  contactSection: {
    title: "Kontaktieren Sie uns",
    subtitle: "Haben Sie Fragen zu unseren Produkten oder benötigen Hilfe bei Ihrer Bestellung? Wir sind hier, um Ihnen zu helfen.",
    details: [
      { text: '+49 30 12345678' },
      { text: '<EMAIL>' },
    ],
    socialAriaLabels: {
      facebook: 'Facebook',
      instagram: 'Instagram',
      pinterest: 'Pinterest',
    },
    form: {
      nameLabel: "Name",
      emailLabel: "E-Mail-Adresse",
      messageLabel: "Nachricht",
    },
    toast: {
      successTitle: "Nachricht gesendet",
      successDescription: "Vielen Dank für Ihre Kontaktaufnahme. Wir werden bald antworten.",
      apiErrorDescription: "Es gab ein Problem beim Senden Ihrer Nachricht. Bitte versuchen Sie es später erneut."
    }
  },

  // Checkout Page
  checkoutPage: {
    title: "Bestellübersicht",
    yourOrder: "Ihre Bestellung",
    customerInfo: "Kundeninformationen",
    shippingAddress: "Lieferadresse",
    billingAddress: "Rechnungsadresse",
    sameAsShipping: "Gleiche Adresse für Rechnung verwenden",
    itemColumnHeader: "Artikel",
    quantityColumnHeader: "Menge",
    priceColumnHeader: "Einzelpreis",
    totalColumnHeader: "Gesamt",
    unitPrice: "Preis:",
    form: {
      firstName: "Vorname",
      lastName: "Nachname",
      email: "E-Mail-Adresse",
      phone: "Telefonnummer",
      company: "Unternehmen (optional)",
      address: "Adresse",
      address2: "Wohnung, Suite, etc. (optional)",
      city: "Stadt",
      state: "Bundesland",
      postalCode: "Postleitzahl",
      country: "Land",
      firstNamePlaceholder: "Ihr Vorname",
      lastNamePlaceholder: "Ihr Nachname",
      emailPlaceholder: "<EMAIL>",
      phonePlaceholder: "+49 30 12345678",
      companyPlaceholder: "Firmenname",
      addressPlaceholder: "Straße und Hausnummer",
      address2Placeholder: "Wohnung, Suite, etc.",
      cityPlaceholder: "Stadt",
      statePlaceholder: "Bundesland",
      postalCodePlaceholder: "12345",
      countryPlaceholder: "Deutschland",
    },
    orderSummary: {
      title: "Bestellzusammenfassung",
      subtotal: "Zwischensumme:",
      shipping: "Versand:",
      shippingEstimate: "15,00 €",
      taxes: "Steuern (Geschätzt):",
      taxesEstimate: "Im nächsten Schritt berechnet",
      grandTotal: "Gesamtsumme:",
      calculating: "Berechnung läuft...",
    },
    proceedToPaymentButton: "Zur Zahlung fortfahren",
    emptyCartMessage: "Ihr Warenkorb ist leer. Sie können nicht zur Kasse gehen.",
    continueShoppingButton: "Katalog erkunden",
    aria: {
      productImage: "Produktbild"
    },
    validation: {
      firstNameRequired: "Der Vorname ist erforderlich",
      lastNameRequired: "Der Nachname ist erforderlich",
      emailRequired: "Die E-Mail-Adresse ist erforderlich",
      emailInvalid: "Bitte geben Sie eine gültige E-Mail-Adresse ein",
      phoneRequired: "Die Telefonnummer ist für Kontakt erforderlich",
      shippingAddressRequired: "Die Lieferadresse ist erforderlich",
      shippingCityRequired: "Die Lieferstadt ist erforderlich",
      shippingStateRequired: "Das Lieferbundesland ist erforderlich",
      shippingPostalCodeRequired: "Die Lieferpostleitzahl ist erforderlich",
      shippingCountryRequired: "Das Lieferland ist erforderlich",
      billingAddressRequired: "Die Rechnungsadresse ist erforderlich",
      billingCityRequired: "Die Rechnungsstadt ist erforderlich",
      billingStateRequired: "Das Rechnungsbundesland ist erforderlich",
      billingPostalCodeRequired: "Die Rechnungspostleitzahl ist erforderlich",
      billingCountryRequired: "Das Rechnungsland ist erforderlich",
      shippingInfoNote: "Diese Informationen sind für die Lieferung Ihrer Bestellung erforderlich",
      billingInfoNote: "Nur erforderlich, wenn abweichend von der Lieferadresse",
    },
    toast: {
      infoSavedTitle: "Informationen gespeichert",
      infoSavedDescription: "Ihre Informationen wurden erfolgreich gespeichert.",
      validationErrorTitle: "Validierungsfehler",
      validationErrorDescription: "Bitte füllen Sie alle erforderlichen Felder aus.",
      itemRemovedTitle: "Produkt entfernt",
      itemRemovedDescription: "Das Produkt wurde aus Ihrem Warenkorb entfernt.",
      quantityExceedsStockTitle: "Unzureichender Bestand",
      quantityExceedsStockDescription: "Sie können nur bis zu",
    },
    constructionBanner: {
      title: "🚧 Seite im Aufbau",
      description: "Diese Funktionalität wird bald verfügbar sein. Vielen Dank für Ihre Geduld!",
    },
  },

  // Payment Page
  paymentPage: {
    title: "Zahlung durchführen",
    orderSummaryTitle: "Ihre Bestellzusammenfassung",
    subtotalLabel: "Zwischensumme:",
    shippingLabel: "Versand:",
    taxesLabel: "Steuern:",
    totalLabel: "Zu zahlender Betrag:",
    selectPaymentMethod: "Wählen Sie Ihre Zahlungsmethode",
    payWithPayPal: "Mit PayPal bezahlen",
    payWithCard: "Mit Karte bezahlen",
    cardDetails: "Kartendetails",
    payButton: "Bezahlen",
    processingPayment: "Zahlung wird verarbeitet...",
    paymentSuccessfulTitle: "Zahlung erfolgreich!",
    paymentSuccessfulDescription: "Ihre Bestellung wurde verarbeitet. Vielen Dank für Ihren Einkauf.",
    paymentFailedTitle: "Zahlungsfehler",
    paymentFailedDescription: "Wir konnten Ihre Zahlung nicht verarbeiten. Bitte versuchen Sie es erneut oder kontaktieren Sie den Support.",
    paypalOrderCreationError: "Fehler beim Erstellen der PayPal-Bestellung.",
    paypalCaptureError: "Fehler beim Erfassen der PayPal-Bestellung.",
    stripePaymentError: "Fehler bei der Zahlungsverarbeitung mit Stripe.",
    stripeSetupError: "Fehler beim Einrichten von Stripe. Überprüfen Sie Ihre API-Schlüssel.",
    missingClientSecret: "Konfigurationsfehler: Stripe client_secret fehlt.",
    paypalUnavailable: "PayPal ist derzeit nicht verfügbar. Versuchen Sie es später erneut.",
    stripeUnavailable: "Stripe ist derzeit nicht verfügbar. Versuchen Sie es später erneut."
  }
};
