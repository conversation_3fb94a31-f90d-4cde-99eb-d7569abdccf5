// src/helpers/i18n/locales/es.ts

import type { AppStrings } from '../types';

export const esStrings: AppStrings = {
  // Site-wide
  site: {
    name: "Luminara",
    meta: {
      title: "Luminara | Velas Artesanales",
      description: "Velas artesanales hechas con ceras naturales y aceites esenciales puros para crear ambientes únicos ❤️",
    },
  },

  // Navigation
  nav: {
    homeLabel: 'Inicio',
    productsLabel: 'Productos',
    aboutLabel: 'Nosotros',
    testimonialsLabel: 'Testimonios',
    contactLabel: 'Contacto',
    home: 'Inicio',
    contact: 'Contacto',
    links: [
      { href: '/#inicio', labelKey: 'homeLabel' },
      { href: '/#productos', labelKey: 'productsLabel' },
      { href: '/#nosotros', labelKey: 'aboutLabel' },
      { href: '/#testimonios', labelKey: 'testimonialsLabel' },
      { href: '/#contacto', labelKey: 'contactLabel' },
    ],
    aria: {
      shoppingCart: "Carrito de compras",
      openMenu: "Abrir menú",
      closeMenu: "Cerrar menú",
      catalogTitle: "Catálogo de Velas",
    }
  },

  // Common UI elements
  common: {
    addToCart: "Agregar",
    exploreCollection: "Explorar colección",
    subscribe: "Suscribirse",
    sendMessage: "Enviar mensaje",
    goBackButtonLabel: "Regresar",
    errorTitle: "Error",
  },

  // Hero Section
  heroSection: {
    title: "Ilumina tu vida con aromas",
    subtitle: "Velas artesanales hechas con ceras naturales y aceites esenciales puros para crear ambientes únicos ❤️",
  },

  // Newsletter Section
  newsletterSection: {
    title: "Únete a nuestra comunidad",
    subtitle: "Suscríbete a nuestro newsletter y recibe un 10% de descuento en tu primera compra, además de contenido exclusivo y novedades.",
    emailPlaceholder: "Tu correo electrónico",
    emailAriaLabel: "Correo electrónico para suscribirse",
    toast: {
      successTitle: "¡Suscripción exitosa!",
      successDescription: "Gracias por unirte a nuestra comunidad.",
      errorTitle: "Error",
      errorDescription: "Por favor, ingresa un correo electrónico válido.",
      apiErrorDescription: "Hubo un problema al procesar tu suscripción. Por favor, inténtalo más tarde."
    }
  },

  // Contact Section
  contactSection: {
    title: "Contáctanos",
    subtitle: "¿Tienes preguntas sobre nuestros productos o necesitas ayuda con tu pedido? Estamos aquí para ayudarte.",
    details: [
      { text: '+52 999 441 6321' },
      { text: '<EMAIL>' },
    ],
    socialAriaLabels: {
      facebook: 'Facebook',
      instagram: 'Instagram',
      pinterest: 'Pinterest',
    },
    form: {
      nameLabel: "Nombre",
      emailLabel: "Correo electrónico",
      messageLabel: "Mensaje",
    },
    toast: {
      successTitle: "Mensaje enviado",
      successDescription: "Gracias por contactarnos. Te responderemos pronto.",
      apiErrorDescription: "Hubo un problema al enviar tu mensaje. Por favor, inténtalo más tarde."
    }
  },

  // Checkout Page
  checkoutPage: {
    title: "Revisión de tu Pedido",
    yourOrder: "Tu Pedido",
    customerInfo: "Información del Cliente",
    shippingAddress: "Dirección de Envío",
    billingAddress: "Dirección de Facturación",
    sameAsShipping: "Usar la misma dirección de envío",
    itemColumnHeader: "Artículo",
    quantityColumnHeader: "Cantidad",
    priceColumnHeader: "Precio Unitario",
    totalColumnHeader: "Total",
    unitPrice: "Precio:",
    form: {
      firstName: "Nombre(s)",
      lastName: "Apellidos",
      email: "Correo electrónico",
      phone: "Teléfono",
      company: "Empresa (opcional)",
      address: "Dirección",
      address2: "Apartamento, suite, etc. (opcional)",
      city: "Ciudad",
      state: "Estado",
      postalCode: "Código postal",
      country: "País",
      firstNamePlaceholder: "Tu nombre",
      lastNamePlaceholder: "Tus apellidos",
      emailPlaceholder: "<EMAIL>",
      phonePlaceholder: "+52 ************",
      companyPlaceholder: "Nombre de la empresa",
      addressPlaceholder: "Calle y número",
      address2Placeholder: "Apartamento, suite, etc.",
      cityPlaceholder: "Ciudad",
      statePlaceholder: "Estado",
      postalCodePlaceholder: "12345",
      countryPlaceholder: "México",
    },
    orderSummary: {
      title: "Resumen del Pedido",
      subtotal: "Subtotal:",
      shipping: "Envío:",
      shippingEstimate: "$150.00",
      taxes: "IVA (15% Estimado):",
      taxesEstimate: "Calculados en el siguiente paso",
      grandTotal: "Total General:",
      calculating: "Calculando...",
    },
    proceedToPaymentButton: "Continuar al Pago",
    emptyCartMessage: "Tu carrito está vacío. No puedes proceder al pago.",
    continueShoppingButton: "Explorar Catálogo",
    aria: {
      productImage: "Imagen de producto"
    },
    validation: {
      firstNameRequired: "El nombre es necesario",
      lastNameRequired: "Los apellidos son necesarios",
      emailRequired: "El correo electrónico es requerido",
      emailInvalid: "Por favor, ingresa un correo electrónico válido",
      phoneRequired: "El teléfono es necesario para contactarte",
      shippingAddressRequired: "La dirección de envío es necesaria",
      shippingCityRequired: "La ciudad de envío es requerida",
      shippingStateRequired: "El estado de envío es requerido",
      shippingPostalCodeRequired: "El código postal de envío es necesario",
      shippingCountryRequired: "El país de envío es requerido",
      billingAddressRequired: "La dirección de facturación es necesaria",
      billingCityRequired: "La ciudad de facturación es requerida",
      billingStateRequired: "El estado de facturación es requerido",
      billingPostalCodeRequired: "El código postal de facturación es necesario",
      billingCountryRequired: "El país de facturación es requerido",
      shippingInfoNote: "Esta información es necesaria para el envío de tu pedido",
      billingInfoNote: "Solo necesaria si es diferente a la dirección de envío",
    },
    toast: {
      infoSavedTitle: "Información guardada",
      infoSavedDescription: "Tu información ha sido guardada correctamente.",
      validationErrorTitle: "Error de validación",
      validationErrorDescription: "Por favor, completa todos los campos requeridos.",
      itemRemovedTitle: "Producto eliminado",
      itemRemovedDescription: "El producto ha sido eliminado de tu carrito.",
      quantityExceedsStockTitle: "Stock insuficiente",
      quantityExceedsStockDescription: "Solo puedes agregar hasta",
    },
    constructionBanner: {
      title: "🚧 Página en Construcción",
      description: "Esta funcionalidad estará disponible pronto. ¡Gracias por tu paciencia!",
    },
  },

  // Payment Page
  paymentPage: {
    title: "Realizar Pago",
    orderSummaryTitle: "Resumen de tu Pedido",
    subtotalLabel: "Subtotal:",
    shippingLabel: "Envío:",
    taxesLabel: "Impuestos:",
    totalLabel: "Total a Pagar:",
    selectPaymentMethod: "Selecciona tu Método de Pago",
    payWithPayPal: "Pagar con PayPal",
    payWithCard: "Pagar con Tarjeta",
    cardDetails: "Detalles de la Tarjeta",
    payButton: "Pagar",
    processingPayment: "Procesando pago...",
    paymentSuccessfulTitle: "¡Pago Exitoso!",
    paymentSuccessfulDescription: "Tu pedido ha sido procesado. Gracias por tu compra.",
    paymentFailedTitle: "Error en el Pago",
    paymentFailedDescription: "No se pudo procesar tu pago. Por favor, intenta de nuevo o contacta a soporte.",
    paypalOrderCreationError: "Error al crear el pedido de PayPal.",
    paypalCaptureError: "Error al capturar el pedido de PayPal.",
    stripePaymentError: "Error al procesar el pago con Stripe.",
    stripeSetupError: "Error al configurar Stripe. Verifica tus claves de API.",
    missingClientSecret: "Error de configuración: Falta el client_secret de Stripe.",
    paypalUnavailable: "PayPal no está disponible en este momento. Intenta más tarde.",
    stripeUnavailable: "Stripe no está disponible en este momento. Intenta más tarde."
  },

  // Catalog Page
  catalogPage: {
    title: "Catálogo",
    bannerImageAlt: "Banner del catálogo",
    searchPlaceholder: "Buscar productos...",
    tagInputPlaceholder: "Buscar por tags (ej: Floral, Fresco, ...)",
    clearFilters: "Limpiar filtros",
    checkoutButtonLabel: "Ir al carrito",
    cartTotalSummaryPrefix: "Total:",
    filteringByName: "Filtrando por nombre:",
    filteringByTags: "Filtrando por tags:",
    noResults: "No se encontraron productos",
    emptyCart: "No hay productos disponibles",
    addedToCartSuffix: "agregado al carrito",
    cartTotalPrefix: "Total del carrito:",
    sizeLabel: "Tamaño:",
    quantityLabel: "Cantidad",
    shippingClauses: "Envío gratis en pedidos mayores a $500",
    availability: {
      available: "Disponible",
      unavailable: "Agotado"
    },
    pagination: {
      previous: "Anterior",
      next: "Siguiente",
      page: "Página",
      of: "de"
    },
    constructionBanner: {
      title: "En construcción",
      description: "Esta funcionalidad está en desarrollo"
    }
  },

  // Footer
  footer: {
    tagline: "Ilumina tu vida con aromas naturales",
    quickLinksTitle: "Enlaces Rápidos",
    policyLinksTitle: "Políticas",
    paymentMethodsTitle: "Métodos de Pago",
    copyright: (year: number) => `© ${year} Luminara. Todos los derechos reservados.`,
    policyLinks: [
      { href: "/politica-de-privacidad", text: "Política de Privacidad" },
      { href: "/terminos-y-condiciones", text: "Términos y Condiciones" },
      { href: "/politica-de-envios", text: "Política de Envíos" },
      { href: "/politica-de-devoluciones", text: "Política de Devoluciones" }
    ],
    paymentMethods: [
      { name: "visa", icon: "visa" },
      { name: "mastercard", icon: "mastercard" },
      { name: "paypal", icon: "paypal" }
    ],
    paymentMethodLabels: {
      visa: "Visa",
      mastercard: "Mastercard",
      paypal: "PayPal"
    }
  },

  // About Section
  aboutSection: {
    title: "Sobre Nosotros",
    content: "Creamos velas artesanales con ingredientes naturales",
    imageAlt: "Sobre nosotros",
    paragraph1: "En Luminara, creamos velas artesanales únicas con ceras naturales y aceites esenciales puros.",
    paragraph2: "Cada vela es cuidadosamente elaborada para crear ambientes especiales en tu hogar.",
    features: {
      natural: { text: "100% Natural" },
      handmade: { text: "Hecho a Mano" },
      sustainable: { text: "Sostenible" },
      quality: { text: "Alta Calidad" },
      artisan: { text: "Artesanal" },
      eco: { text: "Ecológico" }
    }
  },

  // Pricing Section
  pricingSection: {
    title: "Nuestros Paquetes",
    packages: []
  },

  // Products Section
  productsSection: {
    title: "Nuestros Productos",
    products: []
  },

  // Testimonials Section
  testimonialsSection: {
    title: "Lo que dicen nuestros clientes",
    testimonials: []
  },

  // Policy Pages
  returnsPage: {
    title: "Política de Devoluciones",
    content: ["Contenido de la política de devoluciones..."]
  },

  shippingPage: {
    title: "Política de Envíos",
    content: ["Contenido de la política de envíos..."]
  },

  privacyPage: {
    title: "Política de Privacidad",
    content: ["Contenido de la política de privacidad..."]
  },

  termsPage: {
    title: "Términos y Condiciones",
    content: ["Contenido de los términos y condiciones..."]
  }
};
