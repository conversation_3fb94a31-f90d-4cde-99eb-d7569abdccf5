// src/helpers/i18n/locales/en.ts

import type { AppStrings } from '../types';

export const enStrings: AppStrings = {
  // Site-wide
  site: {
    name: "Luminara",
    meta: {
      title: "Luminara | Artisan Candles",
      description: "Handcrafted candles made with natural waxes and pure essential oils to create unique atmospheres ❤️",
    },
  },

  // Navigation
  nav: {
    homeLabel: 'Home',
    productsLabel: 'Products',
    aboutLabel: 'About',
    testimonialsLabel: 'Testimonials',
    contactLabel: 'Contact',
    home: 'Home',
    contact: 'Contact',
    links: [
      { href: '/#home', labelKey: 'homeLabel' },
      { href: '/#products', labelKey: 'productsLabel' },
      { href: '/#about', labelKey: 'aboutLabel' },
      { href: '/#testimonials', labelKey: 'testimonialsLabel' },
      { href: '/#contact', labelKey: 'contactLabel' },
    ],
    aria: {
      shoppingCart: "Shopping cart",
      openMenu: "Open menu",
      closeMenu: "Close menu",
      catalogTitle: "Candle Catalog",
    }
  },

  // Common UI elements
  common: {
    addToCart: "Add to Cart",
    exploreCollection: "Explore collection",
    subscribe: "Subscribe",
    sendMessage: "Send message",
    goBackButtonLabel: "Go back",
    errorTitle: "Error",
  },

  // Hero Section
  heroSection: {
    title: "Illuminate your life with aromas",
    subtitle: "Handcrafted candles made with natural waxes and pure essential oils to create unique atmospheres ❤️",
  },

  // Newsletter Section
  newsletterSection: {
    title: "Join our community",
    subtitle: "Subscribe to our newsletter and receive a 10% discount on your first purchase, plus exclusive content and news.",
    emailPlaceholder: "Your email address",
    emailAriaLabel: "Email address for subscription",
    toast: {
      successTitle: "Subscription successful!",
      successDescription: "Thank you for joining our community.",
      errorTitle: "Error",
      errorDescription: "Please enter a valid email address.",
      apiErrorDescription: "There was a problem processing your subscription. Please try again later."
    }
  },

  // Contact Section
  contactSection: {
    title: "Contact us",
    subtitle: "Do you have questions about our products or need help with your order? We're here to help you.",
    details: [
      { text: '+52 999 441 6321' },
      { text: '<EMAIL>' },
    ],
    socialAriaLabels: {
      facebook: 'Facebook',
      instagram: 'Instagram',
      pinterest: 'Pinterest',
    },
    form: {
      nameLabel: "Name",
      emailLabel: "Email address",
      messageLabel: "Message",
    },
    toast: {
      successTitle: "Message sent",
      successDescription: "Thank you for contacting us. We'll respond soon.",
      apiErrorDescription: "There was a problem sending your message. Please try again later."
    }
  },

  // Checkout Page
  checkoutPage: {
    title: "Order Review",
    yourOrder: "Your Order",
    customerInfo: "Customer Information",
    shippingAddress: "Shipping Address",
    billingAddress: "Billing Address",
    sameAsShipping: "Use same address for billing",
    itemColumnHeader: "Item",
    quantityColumnHeader: "Quantity",
    priceColumnHeader: "Unit Price",
    totalColumnHeader: "Total",
    unitPrice: "Price:",
    form: {
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email address",
      phone: "Phone number",
      company: "Company (optional)",
      address: "Address",
      address2: "Apartment, suite, etc. (optional)",
      city: "City",
      state: "State",
      postalCode: "Postal code",
      country: "Country",
      firstNamePlaceholder: "Your first name",
      lastNamePlaceholder: "Your last name",
      emailPlaceholder: "<EMAIL>",
      phonePlaceholder: "****** 123 4567",
      companyPlaceholder: "Company name",
      addressPlaceholder: "Street and number",
      address2Placeholder: "Apartment, suite, etc.",
      cityPlaceholder: "City",
      statePlaceholder: "State",
      postalCodePlaceholder: "12345",
      countryPlaceholder: "United States",
    },
    orderSummary: {
      title: "Order Summary",
      subtotal: "Subtotal:",
      shipping: "Shipping:",
      shippingEstimate: "$15.00",
      taxes: "Tax (Estimated):",
      taxesEstimate: "Calculated at next step",
      grandTotal: "Grand Total:",
      calculating: "Calculating...",
    },
    proceedToPaymentButton: "Continue to Payment",
    emptyCartMessage: "Your cart is empty. You cannot proceed to checkout.",
    continueShoppingButton: "Explore Catalog",
    aria: {
      productImage: "Product image"
    },
    validation: {
      firstNameRequired: "First name is required",
      lastNameRequired: "Last name is required",
      emailRequired: "Email address is required",
      emailInvalid: "Please enter a valid email address",
      phoneRequired: "Phone number is required for contact",
      shippingAddressRequired: "Shipping address is required",
      shippingCityRequired: "Shipping city is required",
      shippingStateRequired: "Shipping state is required",
      shippingPostalCodeRequired: "Shipping postal code is required",
      shippingCountryRequired: "Shipping country is required",
      billingAddressRequired: "Billing address is required",
      billingCityRequired: "Billing city is required",
      billingStateRequired: "Billing state is required",
      billingPostalCodeRequired: "Billing postal code is required",
      billingCountryRequired: "Billing country is required",
      shippingInfoNote: "This information is required for order delivery",
      billingInfoNote: "Only required if different from shipping address",
    },
    toast: {
      infoSavedTitle: "Information saved",
      infoSavedDescription: "Your information has been saved successfully.",
      validationErrorTitle: "Validation error",
      validationErrorDescription: "Please complete all required fields.",
      itemRemovedTitle: "Product removed",
      itemRemovedDescription: "The product has been removed from your cart.",
      quantityExceedsStockTitle: "Insufficient stock",
      quantityExceedsStockDescription: "You can only add up to",
    },
    constructionBanner: {
      title: "🚧 Page Under Construction",
      description: "This functionality will be available soon. Thank you for your patience!",
    },
  },

  // Payment Page
  paymentPage: {
    title: "Make Payment",
    orderSummaryTitle: "Your Order Summary",
    subtotalLabel: "Subtotal:",
    shippingLabel: "Shipping:",
    taxesLabel: "Taxes:",
    totalLabel: "Total to Pay:",
    selectPaymentMethod: "Select Your Payment Method",
    payWithPayPal: "Pay with PayPal",
    payWithCard: "Pay with Card",
    cardDetails: "Card Details",
    payButton: "Pay",
    processingPayment: "Processing payment...",
    paymentSuccessfulTitle: "Payment Successful!",
    paymentSuccessfulDescription: "Your order has been processed. Thank you for your purchase.",
    paymentFailedTitle: "Payment Error",
    paymentFailedDescription: "We couldn't process your payment. Please try again or contact support.",
    paypalOrderCreationError: "Error creating PayPal order.",
    paypalCaptureError: "Error capturing PayPal order.",
    stripePaymentError: "Error processing payment with Stripe.",
    stripeSetupError: "Error setting up Stripe. Check your API keys.",
    missingClientSecret: "Configuration error: Missing Stripe client_secret.",
    paypalUnavailable: "PayPal is not available at this time. Try again later.",
    stripeUnavailable: "Stripe is not available at this time. Try again later."
  },

  // Catalog Page
  catalogPage: {
    title: "Catalog",
    bannerImageAlt: "Catalog banner",
    searchPlaceholder: "Search products...",
    tagInputPlaceholder: "Search by tags (e.g., Floral, Fresh, ...)",
    clearFilters: "Clear filters",
    checkoutButtonLabel: "Go to cart",
    cartTotalSummaryPrefix: "Total:",
    filteringByName: "Filtering by name:",
    filteringByTags: "Filtering by tags:",
    noResults: "No products found",
    emptyCart: "No products available",
    addedToCartSuffix: "added to cart",
    cartTotalPrefix: "Cart total:",
    sizeLabel: "Size:",
    quantityLabel: "Quantity",
    shippingClauses: "Free shipping on orders over $500",
    availability: {
      available: "Available",
      unavailable: "Out of stock"
    },
    pagination: {
      previous: "Previous",
      next: "Next",
      page: "Page",
      of: "of"
    },
    constructionBanner: {
      title: "Under construction",
      description: "This functionality is in development"
    }
  },

  // Footer
  footer: {
    tagline: "Light up your life with natural aromas",
    quickLinksTitle: "Quick Links",
    policyLinksTitle: "Policies",
    paymentMethodsTitle: "Payment Methods",
    copyright: (year: number) => `© ${year} Luminara. All rights reserved.`,
    policyLinks: [
      { href: "/politica-de-privacidad", text: "Privacy Policy" },
      { href: "/terminos-y-condiciones", text: "Terms and Conditions" },
      { href: "/politica-de-envios", text: "Shipping Policy" },
      { href: "/politica-de-devoluciones", text: "Returns Policy" }
    ],
    paymentMethods: [
      { name: "visa", icon: "visa" },
      { name: "mastercard", icon: "mastercard" },
      { name: "paypal", icon: "paypal" }
    ],
    paymentMethodLabels: {
      visa: "Visa",
      mastercard: "Mastercard",
      paypal: "PayPal"
    }
  },

  // About Section
  aboutSection: {
    title: "About Us",
    content: "We create handmade candles with natural ingredients",
    imageAlt: "About us",
    paragraph1: "At Luminara, we create unique handmade candles with natural waxes and pure essential oils.",
    paragraph2: "Each candle is carefully crafted to create special environments in your home.",
    features: {
      natural: { text: "100% Natural" },
      handmade: { text: "Handmade" },
      sustainable: { text: "Sustainable" },
      quality: { text: "High Quality" },
      artisan: { text: "Artisan" },
      eco: { text: "Eco-friendly" }
    }
  },

  // Pricing Section
  pricingSection: {
    title: "Our Packages",
    packages: []
  },

  // Products Section
  productsSection: {
    title: "Our Products",
    products: []
  },

  // Testimonials Section
  testimonialsSection: {
    title: "What our customers say",
    testimonials: []
  },

  // Policy Pages
  returnsPage: {
    title: "Returns Policy",
    content: ["Returns policy content..."]
  },

  shippingPage: {
    title: "Shipping Policy",
    content: ["Shipping policy content..."]
  },

  privacyPage: {
    title: "Privacy Policy",
    content: ["Privacy policy content..."]
  },

  termsPage: {
    title: "Terms and Conditions",
    content: ["Terms and conditions content..."]
  }
};
