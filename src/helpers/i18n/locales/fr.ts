// src/helpers/i18n/locales/fr.ts

import type { AppStrings } from '../types';

export const frStrings: AppStrings = {
  // Site-wide
  site: {
    name: "Luminara",
    meta: {
      title: "Luminara | Bougies Artisanales",
      description: "Bougies artisanales fabriquées avec des cires naturelles et des huiles essentielles pures pour créer des ambiances uniques ❤️",
    },
  },

  // Navigation
  nav: {
    homeLabel: 'Accueil',
    productsLabel: 'Produits',
    aboutLabel: 'À propos',
    testimonialsLabel: 'Témoignages',
    contactLabel: 'Contact',
    home: 'Accueil',
    contact: 'Contact',
    links: [
      { href: '/#accueil', labelKey: 'homeLabel' },
      { href: '/#produits', labelKey: 'productsLabel' },
      { href: '/#apropos', labelKey: 'aboutLabel' },
      { href: '/#temoignages', labelKey: 'testimonialsLabel' },
      { href: '/#contact', labelKey: 'contactLabel' },
    ],
    aria: {
      shoppingCart: "Panier d'achat",
      openMenu: "Ouvrir le menu",
      closeMenu: "Fermer le menu",
      catalogTitle: "Catalogue de Bougies",
    }
  },

  // Common UI elements
  common: {
    addToCart: "Ajouter",
    exploreCollection: "Explorer la collection",
    subscribe: "S'abonner",
    sendMessage: "Envoyer le message",
    goBackButtonLabel: "Retour",
    errorTitle: "Erreur",
  },

  // Hero Section
  heroSection: {
    title: "Illuminez votre vie avec des arômes",
    subtitle: "Bougies artisanales fabriquées avec des cires naturelles et des huiles essentielles pures pour créer des ambiances uniques ❤️",
  },

  // Newsletter Section
  newsletterSection: {
    title: "Rejoignez notre communauté",
    subtitle: "Abonnez-vous à notre newsletter et recevez 10% de réduction sur votre premier achat, plus du contenu exclusif et des nouveautés.",
    emailPlaceholder: "Votre adresse email",
    emailAriaLabel: "Adresse email pour l'abonnement",
    toast: {
      successTitle: "Abonnement réussi !",
      successDescription: "Merci de rejoindre notre communauté.",
      errorTitle: "Erreur",
      errorDescription: "Veuillez saisir une adresse email valide.",
      apiErrorDescription: "Il y a eu un problème lors du traitement de votre abonnement. Veuillez réessayer plus tard."
    }
  },

  // Contact Section
  contactSection: {
    title: "Contactez-nous",
    subtitle: "Avez-vous des questions sur nos produits ou besoin d'aide avec votre commande ? Nous sommes là pour vous aider.",
    details: [
      { text: '+33 1 23 45 67 89' },
      { text: '<EMAIL>' },
    ],
    socialAriaLabels: {
      facebook: 'Facebook',
      instagram: 'Instagram',
      pinterest: 'Pinterest',
    },
    form: {
      nameLabel: "Nom",
      emailLabel: "Adresse email",
      messageLabel: "Message",
    },
    toast: {
      successTitle: "Message envoyé",
      successDescription: "Merci de nous avoir contactés. Nous répondrons bientôt.",
      apiErrorDescription: "Il y a eu un problème lors de l'envoi de votre message. Veuillez réessayer plus tard."
    }
  },

  // Checkout Page
  checkoutPage: {
    title: "Révision de Commande",
    yourOrder: "Votre Commande",
    customerInfo: "Informations Client",
    shippingAddress: "Adresse de Livraison",
    billingAddress: "Adresse de Facturation",
    sameAsShipping: "Utiliser la même adresse pour la facturation",
    itemColumnHeader: "Article",
    quantityColumnHeader: "Quantité",
    priceColumnHeader: "Prix Unitaire",
    totalColumnHeader: "Total",
    unitPrice: "Prix :",
    form: {
      firstName: "Prénom",
      lastName: "Nom de famille",
      email: "Adresse email",
      phone: "Téléphone",
      company: "Entreprise (optionnel)",
      address: "Adresse",
      address2: "Appartement, suite, etc. (optionnel)",
      city: "Ville",
      state: "Région",
      postalCode: "Code postal",
      country: "Pays",
      firstNamePlaceholder: "Votre prénom",
      lastNamePlaceholder: "Votre nom de famille",
      emailPlaceholder: "<EMAIL>",
      phonePlaceholder: "+33 1 23 45 67 89",
      companyPlaceholder: "Nom de l'entreprise",
      addressPlaceholder: "Rue et numéro",
      address2Placeholder: "Appartement, suite, etc.",
      cityPlaceholder: "Ville",
      statePlaceholder: "Région",
      postalCodePlaceholder: "75001",
      countryPlaceholder: "France",
    },
    orderSummary: {
      title: "Résumé de Commande",
      subtotal: "Sous-total :",
      shipping: "Livraison :",
      shippingEstimate: "15,00 €",
      taxes: "TVA (Estimée) :",
      taxesEstimate: "Calculée à l'étape suivante",
      grandTotal: "Total Général :",
      calculating: "Calcul en cours...",
    },
    proceedToPaymentButton: "Continuer vers le Paiement",
    emptyCartMessage: "Votre panier est vide. Vous ne pouvez pas procéder au checkout.",
    continueShoppingButton: "Explorer le Catalogue",
    aria: {
      productImage: "Image du produit"
    },
    validation: {
      firstNameRequired: "Le prénom est nécessaire",
      lastNameRequired: "Le nom de famille est nécessaire",
      emailRequired: "L'adresse email est requise",
      emailInvalid: "Veuillez saisir une adresse email valide",
      phoneRequired: "Le téléphone est nécessaire pour le contact",
      shippingAddressRequired: "L'adresse de livraison est nécessaire",
      shippingCityRequired: "La ville de livraison est requise",
      shippingStateRequired: "La région de livraison est requise",
      shippingPostalCodeRequired: "Le code postal de livraison est nécessaire",
      shippingCountryRequired: "Le pays de livraison est requis",
      billingAddressRequired: "L'adresse de facturation est nécessaire",
      billingCityRequired: "La ville de facturation est requise",
      billingStateRequired: "La région de facturation est requise",
      billingPostalCodeRequired: "Le code postal de facturation est nécessaire",
      billingCountryRequired: "Le pays de facturation est requis",
      shippingInfoNote: "Cette information est nécessaire pour la livraison de votre commande",
      billingInfoNote: "Nécessaire seulement si différente de l'adresse de livraison",
    },
    toast: {
      infoSavedTitle: "Informations sauvegardées",
      infoSavedDescription: "Vos informations ont été sauvegardées avec succès.",
      validationErrorTitle: "Erreur de validation",
      validationErrorDescription: "Veuillez compléter tous les champs requis.",
      itemRemovedTitle: "Produit supprimé",
      itemRemovedDescription: "Le produit a été supprimé de votre panier.",
      quantityExceedsStockTitle: "Stock insuffisant",
      quantityExceedsStockDescription: "Vous ne pouvez ajouter que jusqu'à",
    },
    constructionBanner: {
      title: "🚧 Page en Construction",
      description: "Cette fonctionnalité sera bientôt disponible. Merci pour votre patience !",
    },
  },

  // Payment Page
  paymentPage: {
    title: "Effectuer le Paiement",
    orderSummaryTitle: "Résumé de Votre Commande",
    subtotalLabel: "Sous-total :",
    shippingLabel: "Livraison :",
    taxesLabel: "Taxes :",
    totalLabel: "Total à Payer :",
    selectPaymentMethod: "Sélectionnez Votre Méthode de Paiement",
    payWithPayPal: "Payer avec PayPal",
    payWithCard: "Payer par Carte",
    cardDetails: "Détails de la Carte",
    payButton: "Payer",
    processingPayment: "Traitement du paiement...",
    paymentSuccessfulTitle: "Paiement Réussi !",
    paymentSuccessfulDescription: "Votre commande a été traitée. Merci pour votre achat.",
    paymentFailedTitle: "Erreur de Paiement",
    paymentFailedDescription: "Nous n'avons pas pu traiter votre paiement. Veuillez réessayer ou contacter le support.",
    paypalOrderCreationError: "Erreur lors de la création de la commande PayPal.",
    paypalCaptureError: "Erreur lors de la capture de la commande PayPal.",
    stripePaymentError: "Erreur lors du traitement du paiement avec Stripe.",
    stripeSetupError: "Erreur lors de la configuration de Stripe. Vérifiez vos clés API.",
    missingClientSecret: "Erreur de configuration : client_secret Stripe manquant.",
    paypalUnavailable: "PayPal n'est pas disponible en ce moment. Réessayez plus tard.",
    stripeUnavailable: "Stripe n'est pas disponible en ce moment. Réessayez plus tard."
  }
};
