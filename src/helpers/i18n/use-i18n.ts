// src/helpers/i18n/use-i18n.ts

'use client';

import { useState, useEffect } from 'react';
import type { SupportedLocale, AppStrings, LocaleConfig } from './types';
import { detectBrowserLocale, getLocaleConfig, DEFAULT_LOCALE } from './config';
import { localeStrings } from './locales';

interface UseI18nReturn {
  locale: SupportedLocale;
  strings: AppStrings;
  localeConfig: LocaleConfig;
  setLocale: (locale: SupportedLocale) => void;
  isLoading: boolean;
}

/**
 * Custom hook for internationalization
 * Automatically detects browser locale and provides translated strings
 */
export function useI18n(): UseI18nReturn {
  const [locale, setLocaleState] = useState<SupportedLocale>(DEFAULT_LOCALE);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize locale from browser detection
  useEffect(() => {
    const detectedLocale = detectBrowserLocale();
    setLocaleState(detectedLocale);
    setIsLoading(false);
  }, []);

  // Get current strings and config
  const strings = localeStrings[locale] || localeStrings[DEFAULT_LOCALE];
  const localeConfig = getLocaleConfig(locale);

  // Function to manually change locale
  const setLocale = (newLocale: SupportedLocale) => {
    setLocaleState(newLocale);
    // Optionally store in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('luminara-locale', newLocale);
    }
  };

  // Load saved locale from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLocale = localStorage.getItem('luminara-locale') as SupportedLocale;
      if (savedLocale && localeStrings[savedLocale]) {
        setLocaleState(savedLocale);
      }
    }
  }, []);

  return {
    locale,
    strings,
    localeConfig,
    setLocale,
    isLoading,
  };
}

/**
 * Get strings for a specific locale (useful for server-side or static usage)
 */
export function getStringsForLocale(locale: SupportedLocale): AppStrings {
  return localeStrings[locale] || localeStrings[DEFAULT_LOCALE];
}

/**
 * Get locale configuration for a specific locale
 */
export function getConfigForLocale(locale: SupportedLocale): LocaleConfig {
  return getLocaleConfig(locale);
}
