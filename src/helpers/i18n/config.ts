// src/helpers/i18n/config.ts

import type { SupportedLocale, LocaleConfig } from './types';

export const SUPPORTED_LOCALES: SupportedLocale[] = ['es', 'en', 'pt', 'fr', 'de'];

export const LOCALE_CONFIGS: Record<SupportedLocale, LocaleConfig> = {
  es: {
    code: 'es',
    name: 'Español',
    flag: '🇪🇸',
    currency: 'MXN',
    stripeLocale: 'es',
    paypalLocale: 'es_MX',
    defaultCountry: 'México',
  },
  en: {
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
    currency: 'USD',
    stripeLocale: 'en',
    paypalLocale: 'en_US',
    defaultCountry: 'United States',
  },
  pt: {
    code: 'pt',
    name: 'Português',
    flag: '🇧🇷',
    currency: 'BRL',
    stripeLocale: 'pt-BR',
    paypalLocale: 'pt_BR',
    defaultCountry: 'Brasil',
  },
  fr: {
    code: 'fr',
    name: 'Français',
    flag: '🇫🇷',
    currency: 'EUR',
    stripeLocale: 'fr',
    paypalLocale: 'fr_FR',
    defaultCountry: 'France',
  },
  de: {
    code: 'de',
    name: 'Deutsch',
    flag: '🇩🇪',
    currency: 'EUR',
    stripeLocale: 'de',
    paypalLocale: 'de_DE',
    defaultCountry: 'Deutschland',
  },
};

// Browser language detection mapping
export const BROWSER_LOCALE_MAP: Record<string, SupportedLocale> = {
  // Spanish variants
  'es': 'es',
  'es-ES': 'es',
  'es-MX': 'es',
  'es-AR': 'es',
  'es-CO': 'es',
  'es-CL': 'es',
  'es-PE': 'es',
  'es-VE': 'es',
  'es-UY': 'es',
  'es-PY': 'es',
  'es-BO': 'es',
  'es-EC': 'es',
  'es-GT': 'es',
  'es-CR': 'es',
  'es-PA': 'es',
  'es-DO': 'es',
  'es-HN': 'es',
  'es-NI': 'es',
  'es-SV': 'es',
  'es-CU': 'es',
  'es-PR': 'es',

  // English variants
  'en': 'en',
  'en-US': 'en',
  'en-GB': 'en',
  'en-CA': 'en',
  'en-AU': 'en',
  'en-NZ': 'en',
  'en-IE': 'en',
  'en-ZA': 'en',
  'en-IN': 'en',

  // Portuguese variants
  'pt': 'pt',
  'pt-BR': 'pt',
  'pt-PT': 'pt',

  // French variants
  'fr': 'fr',
  'fr-FR': 'fr',
  'fr-CA': 'fr',
  'fr-BE': 'fr',
  'fr-CH': 'fr',

  // German variants
  'de': 'de',
  'de-DE': 'de',
  'de-AT': 'de',
  'de-CH': 'de',
};

export const DEFAULT_LOCALE: SupportedLocale = 'es';

/**
 * Detects the user's preferred locale from browser settings
 */
export function detectBrowserLocale(): SupportedLocale {
  if (typeof window === 'undefined') {
    return DEFAULT_LOCALE;
  }

  // Get browser languages in order of preference
  const browserLanguages = [
    navigator.language,
    ...(navigator.languages || []),
  ];

  // Try to find a supported locale
  for (const lang of browserLanguages) {
    const normalizedLang = lang.toLowerCase();
    
    // Direct match
    if (BROWSER_LOCALE_MAP[normalizedLang]) {
      return BROWSER_LOCALE_MAP[normalizedLang];
    }
    
    // Try language code only (e.g., 'es' from 'es-MX')
    const langCode = normalizedLang.split('-')[0];
    if (BROWSER_LOCALE_MAP[langCode]) {
      return BROWSER_LOCALE_MAP[langCode];
    }
  }

  return DEFAULT_LOCALE;
}

/**
 * Gets locale configuration for a given locale
 */
export function getLocaleConfig(locale: SupportedLocale): LocaleConfig {
  return LOCALE_CONFIGS[locale] || LOCALE_CONFIGS[DEFAULT_LOCALE];
}
