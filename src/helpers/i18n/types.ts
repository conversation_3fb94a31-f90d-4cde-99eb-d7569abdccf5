// src/helpers/i18n/types.ts

export type SupportedLocale = 'es' | 'en' | 'pt' | 'fr' | 'de';

export interface AppStrings {
  // Site-wide
  site: {
    name: string;
    meta: {
      title: string;
      description: string;
    };
  };

  // Navigation
  nav: {
    homeLabel: string;
    productsLabel: string;
    aboutLabel: string;
    testimonialsLabel: string;
    contactLabel: string;
    home: string;
    contact: string;
    links: Array<{ href: string; labelKey: string }>;
    aria: {
      shoppingCart: string;
      openMenu: string;
      closeMenu: string;
      catalogTitle: string;
    };
  };

  // Common UI elements
  common: {
    addToCart: string;
    exploreCollection: string;
    subscribe: string;
    sendMessage: string;
    goBackButtonLabel: string;
    errorTitle: string;
  };

  // Hero Section
  heroSection: {
    title: string;
    subtitle: string;
  };

  // Newsletter Section
  newsletterSection: {
    title: string;
    subtitle: string;
    emailPlaceholder: string;
    emailAriaLabel: string;
    toast: {
      successTitle: string;
      successDescription: string;
      errorTitle: string;
      errorDescription: string;
      apiErrorDescription: string;
    };
  };

  // Contact Section
  contactSection: {
    title: string;
    subtitle: string;
    details: Array<{ text: string }>;
    socialAriaLabels: {
      facebook: string;
      instagram: string;
      pinterest: string;
    };
    form: {
      nameLabel: string;
      emailLabel: string;
      messageLabel: string;
    };
    toast: {
      successTitle: string;
      successDescription: string;
      apiErrorDescription: string;
    };
  };

  // Checkout Page
  checkoutPage: {
    title: string;
    yourOrder: string;
    customerInfo: string;
    shippingAddress: string;
    billingAddress: string;
    sameAsShipping: string;
    itemColumnHeader: string;
    quantityColumnHeader: string;
    priceColumnHeader: string;
    totalColumnHeader: string;
    unitPrice: string;
    form: {
      firstName: string;
      lastName: string;
      email: string;
      phone: string;
      company: string;
      address: string;
      address2: string;
      city: string;
      state: string;
      postalCode: string;
      country: string;
      firstNamePlaceholder: string;
      lastNamePlaceholder: string;
      emailPlaceholder: string;
      phonePlaceholder: string;
      companyPlaceholder: string;
      addressPlaceholder: string;
      address2Placeholder: string;
      cityPlaceholder: string;
      statePlaceholder: string;
      postalCodePlaceholder: string;
      countryPlaceholder: string;
    };
    orderSummary: {
      title: string;
      subtotal: string;
      shipping: string;
      shippingEstimate: string;
      taxes: string;
      taxesEstimate: string;
      grandTotal: string;
      calculating: string;
    };
    proceedToPaymentButton: string;
    emptyCartMessage: string;
    continueShoppingButton: string;
    aria: {
      productImage: string;
    };
    validation: {
      firstNameRequired: string;
      lastNameRequired: string;
      emailRequired: string;
      emailInvalid: string;
      phoneRequired: string;
      shippingAddressRequired: string;
      shippingCityRequired: string;
      shippingStateRequired: string;
      shippingPostalCodeRequired: string;
      shippingCountryRequired: string;
      billingAddressRequired: string;
      billingCityRequired: string;
      billingStateRequired: string;
      billingPostalCodeRequired: string;
      billingCountryRequired: string;
      shippingInfoNote: string;
      billingInfoNote: string;
    };
    toast: {
      infoSavedTitle: string;
      infoSavedDescription: string;
      validationErrorTitle: string;
      validationErrorDescription: string;
      itemRemovedTitle: string;
      itemRemovedDescription: string;
      quantityExceedsStockTitle: string;
      quantityExceedsStockDescription: string;
    };
    constructionBanner: {
      title: string;
      description: string;
    };
  };

  // Payment Page
  paymentPage: {
    title: string;
    orderSummaryTitle: string;
    subtotalLabel: string;
    shippingLabel: string;
    taxesLabel: string;
    totalLabel: string;
    selectPaymentMethod: string;
    payWithPayPal: string;
    payWithCard: string;
    cardDetails: string;
    payButton: string;
    processingPayment: string;
    paymentSuccessfulTitle: string;
    paymentSuccessfulDescription: string;
    paymentFailedTitle: string;
    paymentFailedDescription: string;
    paypalOrderCreationError: string;
    paypalCaptureError: string;
    stripePaymentError: string;
    stripeSetupError: string;
    missingClientSecret: string;
    paypalUnavailable: string;
    stripeUnavailable: string;
  };

  // Catalog Page
  catalogPage?: {
    title: string;
    bannerImageAlt: string;
    searchPlaceholder: string;
    tagInputPlaceholder: string;
    clearFilters: string;
    checkoutButtonLabel: string;
    cartTotalSummaryPrefix: string;
    filteringByName: string;
    filteringByTags: string;
    noResults: string;
    emptyCart: string;
    addedToCartSuffix: string;
    cartTotalPrefix: string;
    sizeLabel: string;
    quantityLabel: string;
    shippingClauses: string;
    availability: {
      available: string;
      unavailable: string;
    };
    pagination: {
      previous: string;
      next: string;
      page: string;
      of: string;
    };
    constructionBanner: {
      title: string;
      description: string;
    };
  };

  // Footer
  footer?: {
    tagline: string;
    quickLinksTitle: string;
    policyLinksTitle: string;
    paymentMethodsTitle: string;
    copyright: (year: number) => string;
    policyLinks: Array<{ href: string; text: string }>;
    paymentMethods: Array<{ name: string; icon: string }>;
    paymentMethodLabels: Record<string, string>;
  };

  // About Section
  aboutSection?: {
    title: string;
    content: string;
    imageAlt: string;
    paragraph1: string;
    paragraph2: string;
    features: Record<string, { text: string }>;
  };

  // Pricing Section
  pricingSection?: {
    title: string;
    packages: Array<{
      name: string;
      price: string;
      features: Array<{ text: string; included: boolean }>;
    }>;
  };

  // Products Section
  productsSection?: {
    title: string;
    products: Array<any>;
  };

  // Testimonials Section
  testimonialsSection?: {
    title: string;
    testimonials: Array<{
      name: string;
      text: string;
      rating: number;
    }>;
  };

  // Policy Pages
  returnsPage?: {
    title: string;
    content: Array<string>;
  };

  shippingPage?: {
    title: string;
    content: Array<string>;
  };

  privacyPage?: {
    title: string;
    content: Array<string>;
  };

  termsPage?: {
    title: string;
    content: Array<string>;
  };
}

export interface LocaleConfig {
  code: SupportedLocale;
  name: string;
  flag: string;
  currency: string;
  stripeLocale: string;
  paypalLocale: string;
  defaultCountry: string;
}
