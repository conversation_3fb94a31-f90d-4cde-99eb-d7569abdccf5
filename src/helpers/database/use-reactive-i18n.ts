// src/helpers/database/use-reactive-i18n.ts
// Hook simplificado y más reactivo para productos multiidioma

'use client';

import { useState, useEffect } from 'react';
import { useI18n } from '../i18n';
import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de idiomas a monedas y regiones
const LOCALE_TO_CURRENCY: Record<string, string> = {
  'es': 'MXN',
  'en': 'USD',
  'pt': 'BRL',
  'fr': 'EUR',
  'de': 'EUR'
};

const LOCALE_TO_REGION: Record<string, string> = {
  'es': 'MX',
  'en': 'US',
  'pt': 'BR',
  'fr': 'EU',
  'de': 'EU'
};

export function useReactiveI18nProducts(targetLocale?: string) {
  const { locale: contextLocale } = useI18n();
  const locale = targetLocale || contextLocale;
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('🎯 useReactiveI18nProducts called with locale:', locale);

  useEffect(() => {
    let isCancelled = false;

    const fetchProducts = async () => {
      if (isCancelled) return;

      try {
        console.log('🔄 Fetching products for locale:', locale);
        setLoading(true);
        setError(null);

        const currency = LOCALE_TO_CURRENCY[locale] || 'MXN';
        const region = LOCALE_TO_REGION[locale] || 'MX';

        console.log('💰 Using currency:', currency, 'region:', region);

        // 1. Obtener productos base
        const { data: baseProducts, error: productsError } = await supabase
          .from('products')
          .select('*');

        if (productsError) throw productsError;
        if (!baseProducts) throw new Error('No products found');

        console.log('📦 Base products:', baseProducts.length);

        // 2. Obtener traducciones
        const productIds = baseProducts.map(p => p.id);
        const { data: translations, error: translationsError } = await supabase
          .from('product_translations')
          .select('*')
          .in('product_id', productIds)
          .eq('language_code', locale);

        console.log('🌍 Translations for', locale, ':', translations?.length || 0);
        console.log('🔍 Translation details:', translations?.map(t => ({
          id: t.product_id,
          name: t.name?.substring(0, 20) + '...'
        })));

        // 3. Obtener precios
        const { data: prices, error: pricesError } = await supabase
          .from('product_prices')
          .select('*')
          .in('product_id', productIds)
          .eq('currency_code', currency)
          .eq('region', region);

        console.log('💰 Prices for', currency, ':', prices?.length || 0);
        console.log('🔍 Price details:', prices?.map(p => ({
          id: p.product_id,
          price: p.price,
          currency: p.currency_code
        })));

        // 4. Obtener tags base y sus traducciones
        const { data: baseTags, error: baseTagsError } = await supabase
          .from('tags')
          .select('id, name');

        const { data: tagTranslations, error: tagTranslationsError } = await supabase
          .from('tag_translations')
          .select('*')
          .eq('language_code', locale);

        console.log('🏷️ Base tags:', baseTags?.length || 0);
        console.log('🏷️ Tag translations for products:', tagTranslations?.length || 0);

        if (isCancelled) return;

        // 5. Crear mapeo de nombre de tag → traducción
        const tagNameToIdMap = new Map(baseTags?.map(t => [t.name, t.id]) || []);
        const tagIdToTranslationMap = new Map(tagTranslations?.map(t => [t.tag_id, t.name]) || []);

        // Mapeo directo: nombre original → traducción
        const tagNameToTranslationMap = new Map();
        baseTags?.forEach(tag => {
          const translatedName = tagIdToTranslationMap.get(tag.id) || tag.name;
          tagNameToTranslationMap.set(tag.name, translatedName);
        });

        console.log('🗺️ Tag name to translation map size:', tagNameToTranslationMap.size);

        // 6. Combinar datos de productos
        const translationMap = new Map(translations?.map(t => [t.product_id, t]) || []);
        const priceMap = new Map(prices?.map(p => [p.product_id, p]) || []);

        console.log('🗺️ Translation map size:', translationMap.size);
        console.log('🗺️ Price map size:', priceMap.size);

        const combinedProducts = baseProducts.map(product => {
          const translation = translationMap.get(product.id);
          const priceData = priceMap.get(product.id);

          console.log(`🔍 Mapping product ${product.id}:`, {
            originalName: product.name,
            translatedName: translation?.name,
            hasTranslation: !!translation,
            originalPrice: product.price,
            newPrice: priceData?.price,
            currency: priceData?.currency_code
          });

          // Extraer precio numérico del string original como fallback
          const fallbackPrice = parseFloat(product.price?.replace(/[$,]/g, '') || '0');

          // Traducir tags del producto
          const originalTags = product.tags || [];
          const translatedTags = originalTags.map((tagName: string) => {
            const translatedName = tagNameToTranslationMap.get(tagName) || tagName;
            return translatedName;
          });

          console.log(`🏷️ Tags for ${product.id}:`, {
            original: originalTags,
            translated: translatedTags
          });

          const mappedProduct = {
            id: product.id,
            name: translation?.name || product.name,
            description: translation?.description || product.description,
            long_description: translation?.long_description || product.long_description,
            price: priceData?.price || fallbackPrice,
            currency: priceData?.currency_code || 'MXN',
            availability: product.availability,
            stock: product.stock,
            size: product.size,
            image_src: product.image_src,
            image_alt: product.image_alt,
            image_hint: product.image_hint,
            tags: translatedTags,
            gallery: product.gallery,
            created_at: product.created_at,
            updated_at: product.updated_at
          };

          console.log(`✅ Final product ${product.id}:`, {
            finalName: mappedProduct.name,
            finalPrice: mappedProduct.price,
            finalCurrency: mappedProduct.currency
          });

          return mappedProduct;
        });

        console.log('✅ Combined products:', combinedProducts.length);
        setProducts(combinedProducts);

      } catch (err) {
        if (!isCancelled) {
          console.error('❌ Error fetching products:', err);
          setError(err instanceof Error ? err.message : 'Error loading products');
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };

    fetchProducts();

    return () => {
      isCancelled = true;
    };
  }, [locale]); // Solo depende del locale

  return { products, loading, error };
}

export function useReactiveI18nTags(targetLocale?: string) {
  const { locale: contextLocale } = useI18n();
  const locale = targetLocale || contextLocale;
  const [tags, setTags] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('🎯 useReactiveI18nTags called with locale:', locale);

  useEffect(() => {
    let isCancelled = false;

    const fetchTags = async () => {
      if (isCancelled) return;

      try {
        console.log('🔄 Fetching tags for locale:', locale);
        setLoading(true);
        setError(null);

        // 1. Obtener tags base
        const { data: baseTags, error: tagsError } = await supabase
          .from('tags')
          .select('*')
          .order('name');

        if (tagsError) throw tagsError;
        if (!baseTags) throw new Error('No tags found');

        // 2. Obtener traducciones de tags
        const { data: translations, error: translationsError } = await supabase
          .from('tag_translations')
          .select('*')
          .eq('language_code', locale);

        console.log('🏷️ Tag translations for', locale, ':', translations?.length || 0);

        if (isCancelled) return;

        // 3. Combinar datos
        const translationMap = new Map(translations?.map(t => [t.tag_id, t.name]) || []);

        const combinedTags = baseTags.map(tag => ({
          id: tag.id,
          name: translationMap.get(tag.id) || tag.name,
          original_name: tag.name
        }));

        console.log('✅ Combined tags:', combinedTags.length);
        setTags(combinedTags);

      } catch (err) {
        if (!isCancelled) {
          console.error('❌ Error fetching tags:', err);
          setError(err instanceof Error ? err.message : 'Error loading tags');
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };

    fetchTags();

    return () => {
      isCancelled = true;
    };
  }, [locale]); // Solo depende del locale

  return { tags, loading, error };
}
