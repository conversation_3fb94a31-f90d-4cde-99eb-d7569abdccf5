// src/helpers/database/use-i18n-products.ts
// Hook personalizado para productos multiidioma

import { useState, useEffect, useCallback } from 'react';
import { useI18n } from '../i18n';
import {
  getProductsWithTranslations,
  getProductWithTranslations,
  getTagsWithTranslations,
  type ProductWithTranslations,
  type TagWithTranslation
} from './i18n-queries';

/**
 * Hook para obtener todos los productos con traducciones
 */
export function useI18nProducts() {
  const { locale } = useI18n();
  const [products, setProducts] = useState<ProductWithTranslations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  console.log('🎯 useI18nProducts hook called with locale:', locale, 'trigger:', refreshTrigger);

  // Función para cargar productos
  const loadProducts = useCallback(async (targetLocale: string) => {
    try {
      console.log('🔄 Loading products for locale:', targetLocale);
      setLoading(true);
      setError(null);
      const data = await getProductsWithTranslations(targetLocale as any);
      console.log('✅ Products loaded:', data.length, 'products');
      setProducts(data);
    } catch (err) {
      console.error('❌ Error loading products:', err);
      setError(err instanceof Error ? err.message : 'Error loading products');
    } finally {
      setLoading(false);
    }
  }, []);

  // Efecto que se ejecuta cuando cambia el locale O el trigger
  useEffect(() => {
    console.log('🔥 useI18nProducts useEffect triggered for locale:', locale, 'trigger:', refreshTrigger);
    loadProducts(locale);
  }, [locale, refreshTrigger, loadProducts]);

  const refetch = useCallback(async () => {
    console.log('🔄 Manual refetch triggered, incrementing trigger');
    setRefreshTrigger(prev => prev + 1);
  }, []);

  return { products, loading, error, refetch };
}

/**
 * Hook para obtener un producto específico con traducciones
 */
export function useI18nProduct(productId: string | null) {
  const { locale } = useI18n();
  const [product, setProduct] = useState<ProductWithTranslations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isCancelled = false;

    const fetchProduct = async () => {
      if (!productId) {
        if (!isCancelled) {
          setProduct(null);
          setLoading(false);
        }
        return;
      }

      try {
        console.log('🔄 Fetching product for locale:', locale, 'productId:', productId);
        setLoading(true);
        setError(null);
        const data = await getProductWithTranslations(productId, locale);

        if (!isCancelled) {
          console.log('✅ Product fetched:', data?.name || 'null');
          setProduct(data);
        }
      } catch (err) {
        if (!isCancelled) {
          console.error('❌ Error fetching product:', err);
          setError(err instanceof Error ? err.message : 'Error loading product');
        }
      } finally {
        if (!isCancelled) {
          setLoading(false);
        }
      }
    };

    fetchProduct();

    return () => {
      isCancelled = true;
    };
  }, [productId, locale]);

  const refetch = useCallback(async () => {
    if (!productId) return;

    try {
      setLoading(true);
      setError(null);
      const data = await getProductWithTranslations(productId, locale);
      setProduct(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error loading product');
    } finally {
      setLoading(false);
    }
  }, [productId, locale]);

  return { product, loading, error, refetch };
}

/**
 * Hook para obtener tags con traducciones
 */
export function useI18nTags() {
  const { locale } = useI18n();
  const [tags, setTags] = useState<TagWithTranslation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  console.log('🎯 useI18nTags hook called with locale:', locale, 'trigger:', refreshTrigger);

  // Función para cargar tags
  const loadTags = useCallback(async (targetLocale: string) => {
    try {
      console.log('🔄 Loading tags for locale:', targetLocale);
      setLoading(true);
      setError(null);
      const data = await getTagsWithTranslations(targetLocale as any);
      console.log('✅ Tags loaded:', data.length, 'tags');
      setTags(data);
    } catch (err) {
      console.error('❌ Error loading tags:', err);
      setError(err instanceof Error ? err.message : 'Error loading tags');
    } finally {
      setLoading(false);
    }
  }, []);

  // Efecto que se ejecuta cuando cambia el locale O el trigger
  useEffect(() => {
    console.log('🔥 useI18nTags useEffect triggered for locale:', locale, 'trigger:', refreshTrigger);
    loadTags(locale);
  }, [locale, refreshTrigger, loadTags]);

  const refetch = useCallback(async () => {
    console.log('🔄 Manual tags refetch triggered, incrementing trigger');
    setRefreshTrigger(prev => prev + 1);
  }, []);

  return { tags, loading, error, refetch };
}

/**
 * Hook para filtrar productos por tags
 */
export function useFilteredProducts(filterTags: string[] = []) {
  const { products, loading, error } = useI18nProducts();
  const [filteredProducts, setFilteredProducts] = useState<ProductWithTranslations[]>([]);

  useEffect(() => {
    if (filterTags.length === 0) {
      setFilteredProducts(products);
    } else {
      const filtered = products.filter(product =>
        filterTags.some(filterTag =>
          product.tags.some(productTag =>
            productTag.toLowerCase().includes(filterTag.toLowerCase())
          )
        )
      );
      setFilteredProducts(filtered);
    }
  }, [products, filterTags]);

  return {
    products: filteredProducts,
    allProducts: products,
    loading,
    error
  };
}

/**
 * Hook para buscar productos por texto
 */
export function useProductSearch(searchTerm: string = '') {
  const { products, loading, error } = useI18nProducts();
  const [searchResults, setSearchResults] = useState<ProductWithTranslations[]>([]);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults(products);
    } else {
      const term = searchTerm.toLowerCase();
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(term) ||
        product.description?.toLowerCase().includes(term) ||
        product.tags.some(tag => tag.toLowerCase().includes(term))
      );
      setSearchResults(filtered);
    }
  }, [products, searchTerm]);

  return {
    products: searchResults,
    allProducts: products,
    loading,
    error
  };
}

/**
 * Hook para obtener productos disponibles (en stock)
 */
export function useAvailableProducts() {
  const { products, loading, error } = useI18nProducts();
  const [availableProducts, setAvailableProducts] = useState<ProductWithTranslations[]>([]);

  useEffect(() => {
    const available = products.filter(product =>
      product.availability && product.stock > 0
    );
    setAvailableProducts(available);
  }, [products]);

  return {
    products: availableProducts,
    allProducts: products,
    loading,
    error
  };
}

/**
 * Hook para formatear precios según el idioma/moneda
 */
export function useFormattedPrice(price: number, currency: string) {
  const { locale } = useI18n();

  const formatPrice = (amount: number, currencyCode: string) => {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      // Fallback si hay error con la moneda
      return `${currencyCode} ${amount.toFixed(2)}`;
    }
  };

  return formatPrice(price, currency);
}

/**
 * Hook para obtener el símbolo de moneda según el idioma
 */
export function useCurrencySymbol() {
  const { locale } = useI18n();

  const getCurrencySymbol = () => {
    switch (locale) {
      case 'es': return { code: 'MXN', symbol: '$' };
      case 'en': return { code: 'USD', symbol: '$' };
      case 'pt': return { code: 'BRL', symbol: 'R$' };
      case 'fr': return { code: 'EUR', symbol: '€' };
      case 'de': return { code: 'EUR', symbol: '€' };
      default: return { code: 'MXN', symbol: '$' };
    }
  };

  return getCurrencySymbol();
}
