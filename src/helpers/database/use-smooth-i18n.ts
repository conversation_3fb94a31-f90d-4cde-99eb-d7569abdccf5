// src/helpers/database/use-smooth-i18n.ts
// Hook con transiciones suaves y overlay de carga

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useI18n } from '../i18n';
import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de idiomas a monedas y regiones
const LOCALE_TO_CURRENCY: Record<string, string> = {
  'es': 'MXN',
  'en': 'USD', 
  'pt': 'BRL',
  'fr': 'EUR',
  'de': 'EUR'
};

const LOCALE_TO_REGION: Record<string, string> = {
  'es': 'MX',
  'en': 'US',
  'pt': 'BR', 
  'fr': 'EU',
  'de': 'EU'
};

interface SmoothLoadingState {
  isTransitioning: boolean;
  targetLocale: string | null;
  progress: number;
}

export function useSmoothI18nProducts() {
  const { locale } = useI18n();
  const [products, setProducts] = useState<any[]>([]);
  const [tags, setTags] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [smoothLoading, setSmoothLoading] = useState<SmoothLoadingState>({
    isTransitioning: false,
    targetLocale: null,
    progress: 0
  });

  console.log('🎯 useSmoothI18nProducts called with locale:', locale);

  const fetchData = useCallback(async (targetLocale: string, showTransition = false) => {
    try {
      if (showTransition) {
        setSmoothLoading({
          isTransitioning: true,
          targetLocale,
          progress: 0
        });
      }

      console.log('🔄 Fetching data for locale:', targetLocale);
      setError(null);

      const currency = LOCALE_TO_CURRENCY[targetLocale] || 'MXN';
      const region = LOCALE_TO_REGION[targetLocale] || 'MX';

      // Progreso: 20%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 20 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 1. Obtener productos base
      const { data: baseProducts, error: productsError } = await supabase
        .from('products')
        .select('*');

      if (productsError) throw productsError;

      // Progreso: 40%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 40 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 2. Obtener traducciones de productos
      const productIds = baseProducts?.map(p => p.id) || [];
      const { data: translations } = await supabase
        .from('product_translations')
        .select('*')
        .in('product_id', productIds)
        .eq('language_code', targetLocale);

      // Progreso: 60%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 60 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 3. Obtener precios
      const { data: prices } = await supabase
        .from('product_prices')
        .select('*')
        .in('product_id', productIds)
        .eq('currency_code', currency)
        .eq('region', region);

      // Progreso: 80%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 80 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 4. Obtener tags y traducciones
      const { data: baseTags } = await supabase
        .from('tags')
        .select('id, name')
        .order('name');

      const { data: tagTranslations } = await supabase
        .from('tag_translations')
        .select('*')
        .eq('language_code', targetLocale);

      // Progreso: 90%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 90 }));
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 5. Procesar datos
      const translationMap = new Map(translations?.map(t => [t.product_id, t]) || []);
      const priceMap = new Map(prices?.map(p => [p.product_id, p]) || []);
      
      // Mapeo de tags
      const tagIdToTranslationMap = new Map(tagTranslations?.map(t => [t.tag_id, t.name]) || []);
      const tagNameToTranslationMap = new Map();
      baseTags?.forEach(tag => {
        const translatedName = tagIdToTranslationMap.get(tag.id) || tag.name;
        tagNameToTranslationMap.set(tag.name, translatedName);
      });

      // Combinar productos
      const combinedProducts = baseProducts?.map(product => {
        const translation = translationMap.get(product.id);
        const priceData = priceMap.get(product.id);
        const fallbackPrice = parseFloat(product.price?.replace(/[$,]/g, '') || '0');

        // Traducir tags
        const originalTags = product.tags || [];
        const translatedTags = originalTags.map((tagName: string) => {
          return tagNameToTranslationMap.get(tagName) || tagName;
        });

        return {
          id: product.id,
          name: translation?.name || product.name,
          description: translation?.description || product.description,
          long_description: translation?.long_description || product.long_description,
          price: priceData?.price || fallbackPrice,
          currency: priceData?.currency_code || 'MXN',
          availability: product.availability,
          stock: product.stock,
          size: product.size,
          image_src: product.image_src,
          image_alt: product.image_alt,
          image_hint: product.image_hint,
          tags: translatedTags,
          gallery: product.gallery,
          created_at: product.created_at,
          updated_at: product.updated_at
        };
      }) || [];

      // Combinar tags
      const combinedTags = baseTags?.map(tag => ({
        id: tag.id,
        name: tagIdToTranslationMap.get(tag.id) || tag.name,
        original_name: tag.name
      })) || [];

      // Progreso: 100%
      if (showTransition) {
        setSmoothLoading(prev => ({ ...prev, progress: 100 }));
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      console.log('✅ Data loaded successfully:', {
        products: combinedProducts.length,
        tags: combinedTags.length,
        locale: targetLocale
      });

      setProducts(combinedProducts);
      setTags(combinedTags);

      // Finalizar transición
      if (showTransition) {
        await new Promise(resolve => setTimeout(resolve, 200));
        setSmoothLoading({
          isTransitioning: false,
          targetLocale: null,
          progress: 0
        });
      }

    } catch (err) {
      console.error('❌ Error fetching data:', err);
      setError(err instanceof Error ? err.message : 'Error loading data');
      
      if (showTransition) {
        setSmoothLoading({
          isTransitioning: false,
          targetLocale: null,
          progress: 0
        });
      }
    } finally {
      setLoading(false);
    }
  }, []);

  // Carga inicial
  useEffect(() => {
    fetchData(locale, false);
  }, []);

  // Función para cambiar idioma con transición
  const changeLocale = useCallback(async (newLocale: string) => {
    if (newLocale === locale) return;
    
    console.log('🌍 Changing locale with smooth transition:', locale, '→', newLocale);
    await fetchData(newLocale, true);
  }, [locale, fetchData]);

  return {
    products,
    tags,
    loading,
    error,
    smoothLoading,
    changeLocale
  };
}
