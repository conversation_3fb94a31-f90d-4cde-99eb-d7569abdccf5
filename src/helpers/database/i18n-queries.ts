// src/helpers/database/i18n-queries.ts
// Funciones para consultar datos multiidioma desde Supabase

import { createClient } from '@supabase/supabase-js';
import type { SupportedLocale } from '../i18n/types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseKey);

// Tipos para los datos multiidioma
export interface ProductWithTranslations {
  id: string;
  name: string;
  description: string | null;
  long_description: string | null;
  price: number;
  currency: string;
  availability: boolean;
  stock: number;
  size: string | null;
  image_src: string | null;
  image_alt: string | null;
  image_hint: string | null;
  tags: string[];
  gallery: string[] | null;
  created_at: string;
  updated_at: string;
}

export interface TagWithTranslation {
  id: number;
  name: string;
  original_name: string;
}

// Mapeo de idiomas a monedas
const LOCALE_TO_CURRENCY: Record<SupportedLocale, string> = {
  es: 'MXN',
  en: 'USD',
  pt: 'BRL',
  fr: 'EUR',
  de: 'EUR'
};

// Mapeo de idiomas a regiones
const LOCALE_TO_REGION: Record<SupportedLocale, string> = {
  es: 'MX',
  en: 'US',
  pt: 'BR',
  fr: 'EU',
  de: 'EU'
};

/**
 * Obtiene todos los productos con traducciones y precios según el idioma
 */
export async function getProductsWithTranslations(
  locale: SupportedLocale = 'es'
): Promise<ProductWithTranslations[]> {
  try {
    console.log('🌍 getProductsWithTranslations called with locale:', locale);
    const currency = LOCALE_TO_CURRENCY[locale];
    const region = LOCALE_TO_REGION[locale];
    console.log('💰 Currency mapping:', { locale, currency, region });

    // Query principal para productos
    console.log('📊 Executing products query...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        id,
        name,
        description,
        long_description,
        price,
        availability,
        stock,
        size,
        image_src,
        image_alt,
        image_hint,
        tags,
        gallery,
        created_at,
        updated_at
      `);

    if (productsError) {
      console.error('❌ Error fetching products:', productsError);
      return [];
    }

    if (!products) {
      console.log('⚠️ No products returned from query');
      return [];
    }

    console.log('✅ Products query successful:', products.length, 'products found');
    console.log('📋 Product IDs:', products.map(p => p.id));

    // Obtener traducciones para el idioma específico
    const productIds = products.map(p => p.id);
    console.log('🔍 Looking for translations for locale:', locale, 'productIds:', productIds);

    const { data: translations, error: translationsError } = await supabase
      .from('product_translations')
      .select('product_id, name, description, long_description')
      .in('product_id', productIds)
      .eq('language_code', locale);

    console.log('🌍 Translations query result:', {
      locale,
      translationsCount: translations?.length || 0,
      error: translationsError,
      translations: translations?.slice(0, 2) // Solo primeras 2 para debug
    });

    // Obtener precios para la moneda específica
    const { data: prices, error: pricesError } = await supabase
      .from('product_prices')
      .select('product_id, price, currency_code')
      .in('product_id', productIds)
      .eq('currency_code', currency)
      .eq('region', region);

    console.log('💰 Prices query result:', {
      currency,
      region,
      pricesCount: prices?.length || 0,
      error: pricesError,
      prices: prices?.slice(0, 2) // Solo primeras 2 para debug
    });

    // Obtener tags traducidos
    const { data: tagTranslations } = await supabase
      .from('tag_translations')
      .select(`
        tag_id,
        name,
        tags!inner(name)
      `)
      .eq('language_code', locale);

    // Crear mapas para búsqueda rápida
    const translationMap = new Map(
      translations?.map(t => [t.product_id, t]) || []
    );

    const priceMap = new Map(
      prices?.map(p => [p.product_id, p]) || []
    );

    const tagMap = new Map(
      tagTranslations?.map(t => [t.tag_id || '', t.name]) || []
    );

    // Combinar datos
    return products.map(product => {
      const translation = translationMap.get(product.id);
      const priceData = priceMap.get(product.id);

      // Traducir tags
      const translatedTags = product.tags?.map((tag: string) =>
        tagMap.get(tag) || tag
      ) || [];

      // Extraer precio numérico del string original como fallback
      const fallbackPrice = parseFloat(product.price?.replace(/[$,]/g, '') || '0');

      return {
        id: product.id,
        name: translation?.name || product.name,
        description: translation?.description || product.description,
        long_description: translation?.long_description || product.long_description,
        price: priceData?.price || fallbackPrice,
        currency: priceData?.currency_code || 'MXN',
        availability: product.availability,
        stock: product.stock,
        size: product.size,
        image_src: product.image_src,
        image_alt: product.image_alt,
        image_hint: product.image_hint,
        tags: translatedTags,
        gallery: product.gallery,
        created_at: product.created_at,
        updated_at: product.updated_at
      };
    });

  } catch (error) {
    console.error('Error in getProductsWithTranslations:', error);
    return [];
  }
}

/**
 * Obtiene un producto específico con traducciones
 */
export async function getProductWithTranslations(
  productId: string,
  locale: SupportedLocale = 'es'
): Promise<ProductWithTranslations | null> {
  try {
    const products = await getProductsWithTranslations(locale);
    return products.find(p => p.id === productId) || null;
  } catch (error) {
    console.error('Error in getProductWithTranslations:', error);
    return null;
  }
}

/**
 * Obtiene todos los tags con traducciones
 */
export async function getTagsWithTranslations(
  locale: SupportedLocale = 'es'
): Promise<TagWithTranslation[]> {
  try {
    // Obtener todos los tags
    const { data: tags, error: tagsError } = await supabase
      .from('tags')
      .select('id, name')
      .order('name');

    if (tagsError || !tags) {
      console.error('Error fetching tags:', tagsError);
      return [];
    }

    // Obtener traducciones para el idioma específico
    const { data: translations } = await supabase
      .from('tag_translations')
      .select('tag_id, name')
      .eq('language_code', locale);

    // Crear mapa de traducciones
    const translationMap = new Map(
      translations?.map(t => [t.tag_id, t.name]) || []
    );

    // Combinar datos
    return tags.map(tag => ({
      id: tag.id,
      name: translationMap.get(tag.id) || tag.name,
      original_name: tag.name
    }));

  } catch (error) {
    console.error('Error in getTagsWithTranslations:', error);
    return [];
  }
}

/**
 * Obtiene precios disponibles para un producto en todas las monedas
 */
export async function getProductPrices(productId: string) {
  try {
    const { data: prices, error } = await supabase
      .from('product_prices')
      .select('currency_code, price, region')
      .eq('product_id', productId)
      .order('currency_code');

    if (error) {
      console.error('Error fetching product prices:', error);
      return [];
    }

    return prices || [];
  } catch (error) {
    console.error('Error in getProductPrices:', error);
    return [];
  }
}

/**
 * Obtiene estadísticas de la base de datos multiidioma
 */
export async function getI18nStats() {
  try {
    const [
      { count: productsCount },
      { count: translationsCount },
      { count: pricesCount },
      { count: tagTranslationsCount }
    ] = await Promise.all([
      supabase.from('products').select('*', { count: 'exact', head: true }),
      supabase.from('product_translations').select('*', { count: 'exact', head: true }),
      supabase.from('product_prices').select('*', { count: 'exact', head: true }),
      supabase.from('tag_translations').select('*', { count: 'exact', head: true })
    ]);

    return {
      products: productsCount || 0,
      translations: translationsCount || 0,
      prices: pricesCount || 0,
      tagTranslations: tagTranslationsCount || 0
    };
  } catch (error) {
    console.error('Error getting i18n stats:', error);
    return {
      products: 0,
      translations: 0,
      prices: 0,
      tagTranslations: 0
    };
  }
}
