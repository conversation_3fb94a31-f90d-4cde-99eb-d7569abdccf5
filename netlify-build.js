#!/usr/bin/env node

/**
 * This is a custom build script for Netlify to ensure successful builds with Next.js 15
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Ensure we run in the project root
process.chdir(path.resolve(__dirname));

console.log('🚀 Starting Netlify custom build process for Next.js');

try {
  // Check for the presence of the App Router API routes that might cause issues
  const apiDirPath = path.join(__dirname, 'src', 'app', 'api');
  
  if (fs.existsSync(apiDirPath)) {
    console.log('⚠️ App Router API routes detected. These can cause issues with Netlify.');
    console.log('ℹ️ Using Netlify Functions instead for API routes.');
  }

  // Run the Next.js build with specific options for Netlify
  console.log('📦 Building Next.js application...');
  execSync('next build', { stdio: 'inherit' });
  
  console.log('✅ Next.js build completed successfully!');
  process.exit(0);
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
