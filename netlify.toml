[build]
  command = "npm run build"
  publish = ".next"
  functions = "netlify/functions"

[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"

# Next.js plugin with Netlify Forms support
[[plugins]]
  package = "@netlify/plugin-nextjs"

[functions]
  node_bundler = "esbuild"

# Ensure forms are processed correctly
[[headers]]
  for = "/__forms.html"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
