# **App Name**: Luminara焕彩

## Core Features:

- HTML/React Parity: Faithful rendering of the provided HTML structure using React components. Ensuring visual parity is maintained.
- Componentization: Componentize all page sections, from the header and hero to the footer. Each component is implemented to mirror its HTML/Tailwind counterpart visually.
- Dynamic content: Use React states to change the value and to control every element in the webpage dynamically.
- Smooth scrolling: Enable smooth scrolling to different sections.

## Style Guidelines:

- Primary color: Amber (#FFB300), embodying warmth and radiance, reflective of candlelight. Selected for its welcoming and luxurious feel.
- Background color: Pale Amber (#FAF0E6), providing a soft and inviting backdrop that complements the primary color without overpowering it. Almost the same hue, it keeps visual consistenty.
- Accent color: Golden Yellow (#FFD700), used sparingly to highlight key interactive elements such as buttons and links, adding a touch of opulence. Also of a nearly identical hue, but is brighter and more saturated.
- Elegant serif fonts for headings and clean sans-serif fonts for body text, for a classic yet readable feel.
- Use Font Awesome icons that reflect candle themes.
- Clean layout to reflect simple content structure.
- Subtle transition effects to product cards.