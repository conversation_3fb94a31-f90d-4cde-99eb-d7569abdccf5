
// netlify/functions/products.js
const { createClient } = require('@supabase/supabase-js');

exports.handler = async (event, context) => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ error: 'Supabase URL or Key not configured in environment variables.' }),
    };
  }

  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Obtener los productos con sus tags
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        *,
        product_tags (
          tags (
            id,
            name
          )
        )
      `);

    if (productsError) {
      console.error('Error fetching products:', productsError);
      throw productsError;
    }

    // Formatear los productos para la respuesta
    const formattedProducts = (products || []).map(product => {
      // Extraer los nombres de los tags de la relación
      const tagsArray = product.product_tags?.map(pt => pt.tags?.name).filter(Boolean) || [];
      
      // Procesar la galería de imágenes
      let galleryArray = [];
      if (product.gallery && Array.isArray(product.gallery)) {
        galleryArray = product.gallery;
      } else if (typeof product.gallery === 'string') {
        try {
          galleryArray = JSON.parse(product.gallery);
          if (!Array.isArray(galleryArray)) galleryArray = [product.gallery];
        } catch (e) {
          galleryArray = product.gallery.split(',').map(url => url.trim()).filter(url => url);
        }
      }
      
      return {
        id: product.id,
        name: product.name,
        description: product.description,
        longDescription: product.long_description || product.long_descripti || '',
        price: product.price,
        availability: product.availability,
        stock: product.stock,
        reorderPoint: product.reorder_point,
        size: product.size,
        imageSrc: product.image_src,
        imageAlt: product.image_alt,
        imageHint: product.image_hint,
        tags: tagsArray,
        gallery: galleryArray,
      };
    });

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formattedProducts),
    };
  } catch (error) {
    console.error('Error in products function:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ 
        error: 'Failed to fetch products.', 
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }),
    };
  }
};
