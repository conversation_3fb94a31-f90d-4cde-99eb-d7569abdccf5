# CONTEXTO COMPLETO: INTERNACIONALIZACIÓN LUMINARA

## 🎯 OBJETIVO
Implementar sistema completo de internacionalización (i18n) para la base de datos de productos de Luminara, incluyendo:
- Traducciones de productos (nombres, descripciones)
- Precios regionales en múltiples monedas
- Tags/etiquetas multiidioma

## 📊 ESTADO ACTUAL

### Base de Datos Supabase
- **URL**: https://speynvlzzknzhpukhpyg.supabase.co
- **Anon Key**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNwZXludmx6emtuemhwdWtocHlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ2MDYsImV4cCI6MjA2MzkzMDYwNn0.0B0qTmRkG8fShvYIJKIADtDksivpXb_2aYiVoJxyh0Q

### Estructura Actual
```sql
-- Tabla products (existente)
products (
  id TEXT PRIMARY KEY,
  name TEXT, -- EN ESPAÑOL
  description TEXT, -- EN ESPAÑOL  
  long_description TEXT, -- EN ESPAÑOL
  price TEXT, -- <PERSON><PERSON> PESOS MEXICANOS ($250.00)
  tags TEXT[], -- <PERSON><PERSON>Ñ<PERSON> ["Relajante", "Floral"]
  availability BOOLEAN,
  stock INTEGER,
  size TEXT,
  image_src TEXT,
  gallery TEXT[],
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Tabla tags (existente)
tags (
  id SERIAL PRIMARY KEY,
  name TEXT -- EN ESPAÑOL
)
```

### Datos de Ejemplo
```json
{
  "id": "vela-aromatica-lavanda",
  "name": "Vela Relajante de Lavanda",
  "description": "Calma y serenidad con aceite esencial de lavanda pura.",
  "price": "$250.00",
  "tags": ["Relajante", "Floral", "Aromaterapia"]
}
```

## 🚀 PLAN DE IMPLEMENTACIÓN

### Fase 1: Crear Nuevas Tablas ✅ PREPARADO
```sql
-- 1. product_translations
CREATE TABLE product_translations (
  id SERIAL PRIMARY KEY,
  product_id TEXT REFERENCES products(id),
  language_code VARCHAR(5) CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  description TEXT,
  long_description TEXT,
  UNIQUE(product_id, language_code)
);

-- 2. product_prices  
CREATE TABLE product_prices (
  id SERIAL PRIMARY KEY,
  product_id TEXT REFERENCES products(id),
  currency_code VARCHAR(3) CHECK (currency_code IN ('MXN', 'USD', 'EUR', 'BRL')),
  price DECIMAL(10,2) NOT NULL,
  region VARCHAR(5) CHECK (region IN ('MX', 'US', 'EU', 'BR')),
  UNIQUE(product_id, currency_code, region)
);

-- 3. tag_translations
CREATE TABLE tag_translations (
  id SERIAL PRIMARY KEY,
  tag_id INTEGER REFERENCES tags(id),
  language_code VARCHAR(5) CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  UNIQUE(tag_id, language_code)
);
```

### Fase 2: Migrar Datos ✅ PREPARADO
- Convertir precios MXN existentes
- Calcular precios en USD, EUR, BRL
- Crear traducciones de ejemplo
- Migrar tags a sistema multiidioma

### Fase 3: Actualizar Frontend ⏳ PENDIENTE
- Crear funciones de consulta multiidioma
- Actualizar componentes para usar nuevas APIs
- Integrar con sistema i18n existente

## 📁 ARCHIVOS CREADOS

### Scripts SQL
- `scripts/supabase-i18n-schema.sql` - Schema completo para ejecutar en Supabase
- `scripts/create-i18n-tables.sql` - Versión detallada con comentarios
- `scripts/migrate-database.js` - Script de migración de datos

### Sistema i18n Frontend ✅ COMPLETADO
- `src/helpers/i18n/` - Sistema completo de internacionalización
- 5 idiomas: es, en, pt, fr, de
- Detección automática del navegador
- Selector de idioma en header
- Configuración de Stripe/PayPal por idioma

## 🔧 CONFIGURACIÓN TÉCNICA

### Tasas de Cambio (MXN base)
```javascript
const exchangeRates = {
  'USD': 0.055,  // 1 MXN = 0.055 USD
  'EUR': 0.051,  // 1 MXN = 0.051 EUR  
  'BRL': 0.32    // 1 MXN = 0.32 BRL
};
```

### Mapeo de Regiones
```javascript
const regions = {
  'USD': 'US',
  'EUR': 'EU', 
  'BRL': 'BR',
  'MXN': 'MX'
};
```

## 🎯 PRÓXIMO PASO INMEDIATO

**EJECUTAR EN SUPABASE SQL EDITOR:**
```sql
-- Contenido de scripts/supabase-i18n-schema.sql
-- Crear todas las tablas de internacionalización
```

**LUEGO EJECUTAR:**
```bash
node scripts/migrate-database.js
```

## 🔍 VERIFICACIÓN DE PROGRESO

### ✅ Completado
- Sistema i18n frontend (5 idiomas)
- Detección automática de idioma
- Selector de idioma en header
- Configuración de pagos por idioma
- Scripts SQL preparados
- Scripts de migración preparados

### ⏳ En Progreso
- Creación de tablas en Supabase
- Migración de datos existentes

### 📋 Pendiente
- Funciones de consulta multiidioma
- Actualización de componentes frontend
- Testing completo del sistema

## 🚨 NOTAS IMPORTANTES

1. **Compatibilidad**: El sistema mantiene total compatibilidad con código existente
2. **Fallback**: Español como idioma base para todas las traducciones
3. **Performance**: Índices optimizados para consultas multiidioma
4. **Seguridad**: RLS habilitado en todas las nuevas tablas

## 🔗 DEPENDENCIAS

- Supabase configurado y funcionando
- Node.js y npm instalados
- Acceso a panel de Supabase para ejecutar SQL
- Variables de entorno configuradas en .env.local
