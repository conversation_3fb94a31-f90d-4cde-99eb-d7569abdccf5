<!DOCTYPE html>
<html>
  <head>
    <title>Netlify Forms Detection</title>
  </head>
  <body>
    <!-- Contact Form for detection -->
    <form name="contact" data-netlify="true" hidden>
      <input type="hidden" name="form-name" value="contact" />
      <input name="name" type="text" />
      <input name="email" type="email" />
      <textarea name="message"></textarea>
    </form>

    <!-- Newsletter Form for detection -->
    <form name="newsletter" data-netlify="true" data-netlify-honeypot="bot-field-newsletter" hidden>
      <input type="hidden" name="form-name" value="newsletter" />
      <input name="email" type="email" />
      <!-- Honeypot field for spam protection -->
      <input name="bot-field-newsletter" style="display: none;" />
    </form>

    <!-- Checkout Customer Info Form for detection -->
    <form name="checkout-info" data-netlify="true" data-netlify-honeypot="bot-field-checkout" hidden>
      <input type="hidden" name="form-name" value="checkout-info" />
      <!-- Customer Info -->
      <input name="firstName" type="text" />
      <input name="lastName" type="text" />
      <input name="email" type="email" />
      <input name="phone" type="tel" />
      <input name="company" type="text" />
      <!-- Shipping Address -->
      <input name="shippingAddress" type="text" />
      <input name="shippingAddress2" type="text" />
      <input name="shippingCity" type="text" />
      <input name="shippingState" type="text" />
      <input name="shippingPostalCode" type="text" />
      <input name="shippingCountry" type="text" />
      <!-- Billing Address -->
      <input name="sameAsShipping" type="checkbox" />
      <input name="billingAddress" type="text" />
      <input name="billingAddress2" type="text" />
      <input name="billingCity" type="text" />
      <input name="billingState" type="text" />
      <input name="billingPostalCode" type="text" />
      <input name="billingCountry" type="text" />
      <!-- Order Info -->
      <textarea name="orderItems"></textarea>
      <input name="orderTotal" type="text" />
      <!-- Honeypot field for spam protection -->
      <input name="bot-field-checkout" style="display: none;" />
    </form>
  </body>
</html>
