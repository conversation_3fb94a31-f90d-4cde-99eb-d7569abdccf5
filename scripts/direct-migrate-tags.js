// scripts/direct-migrate-tags.js
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://speynvlzzknzhpukhpyg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNwZXludmx6emtuemhwdWtocHlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTY3NjUyODgsImV4cCI6MjAzMjM0MTI4OH0.2mX3t6X3J8QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9';

// Inicializar cliente de Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateTags() {
  try {
    console.log('Iniciando migración de tags...');
    
    // Leer el archivo existencia.json
    const data = await fs.readFile(path.join(__dirname, '../public/data/existencia.json'), 'utf8');
    const products = JSON.parse(data);
    
    // Extraer todos los tags únicos
    const allTags = new Set();
    const productTags = [];
    
    products.forEach(product => {
      if (product.tags && Array.isArray(product.tags)) {
        product.tags.forEach(tag => {
          if (tag && typeof tag === 'string') {
            allTags.add(tag.trim());
            productTags.push({
              product_id: product.id,
              tag_name: tag.trim()
            });
          }
        });
      }
    });
    
    console.log(`Se encontraron ${allTags.size} tags únicos en ${products.length} productos`);
    
    // Verificar si ya existen tags en la base de datos
    const { data: existingTags, error: fetchError } = await supabase
      .from('tags')
      .select('name');
    
    if (fetchError) {
      console.error('Error al obtener tags existentes:', fetchError);
      return;
    }
    
    const existingTagNames = new Set(existingTags.map(t => t.name));
    const newTags = Array.from(allTags).filter(tag => !existingTagNames.has(tag));
    
    // Insertar tags que no existen
    if (newTags.length > 0) {
      const { error: insertError } = await supabase
        .from('tags')
        .insert(newTags.map(name => ({ name })));
      
      if (insertError) {
        console.error('Error al insertar nuevos tags:', insertError);
        return;
      }
      console.log(`Se insertaron ${newTags.length} nuevos tags`);
    } else {
      console.log('No hay tags nuevos para insertar');
    }
    
    // Obtener todos los tags con sus IDs
    const { data: allTagsFromDb, error: tagsError } = await supabase
      .from('tags')
      .select('id, name');
    
    if (tagsError) {
      console.error('Error al obtener tags de la base de datos:', tagsError);
      return;
    }
    
    const tagNameToId = {};
    allTagsFromDb.forEach(tag => {
      tagNameToId[tag.name] = tag.id;
    });
    
    // Preparar relaciones de product_tags
    const productTagRelations = [];
    const existingRelations = new Set();
    
    // Verificar relaciones existentes
    const { data: existingProductTags, error: relationsError } = await supabase
      .from('product_tags')
      .select('product_id, tag_id');
    
    if (relationsError) {
      console.error('Error al obtener relaciones existentes:', relationsError);
    } else {
      existingProductTags.forEach(rel => {
        existingRelations.add(`${rel.product_id}-${rel.tag_id}`);
      });
      console.log(`Se encontraron ${existingRelations.size} relaciones existentes`);
    }
    
    // Crear nuevas relaciones
    const newRelations = [];
    
    for (const rel of productTags) {
      const tagId = tagNameToId[rel.tag_name];
      if (tagId && !existingRelations.has(`${rel.product_id}-${tagId}`)) {
        newRelations.push({
          product_id: rel.product_id,
          tag_id: tagId
        });
      }
    }
    
    console.log(`Se insertarán ${newRelations.length} nuevas relaciones`);
    
    // Insertar relaciones en lotes de 100
    const batchSize = 100;
    for (let i = 0; i < newRelations.length; i += batchSize) {
      const batch = newRelations.slice(i, i + batchSize);
      const { error: insertRelError } = await supabase
        .from('product_tags')
        .insert(batch);
      
      if (insertRelError) {
        console.error(`Error al insertar lote de relaciones (${i} - ${i + batch.length - 1}):`, insertRelError);
      } else {
        console.log(`Insertadas relaciones ${i + 1} a ${Math.min(i + batch.length, newRelations.length)}`);
      }
    }
    
    console.log('Migración completada exitosamente');
    
  } catch (error) {
    console.error('Error durante la migración:', error);
  }
}

// Ejecutar la migración
migrateTags();
