// scripts/run-tags-migration.js
const readline = require('readline').createInterface({
  input: process.stdin,
  output: process.stdout
});

const { exec } = require('child_process');

readline.question('Por favor ingresa tu URL de Supabase: ', (url) => {
  const supabaseUrl = url.trim();
  
  readline.question('Por favor ingresa tu clave anónima de Supabase: ', (key) => {
    const supabaseKey = key.trim();
    
    console.log('\nIniciando la migración de tags...\n');
    
    // Establecer las variables de entorno
    process.env.NEXT_PUBLIC_SUPABASE_URL = supabaseUrl;
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = supabaseKey;
    
    // Importar y ejecutar el script de migración
    require('./populate-product-tags');
    
    readline.close();
  });
});
