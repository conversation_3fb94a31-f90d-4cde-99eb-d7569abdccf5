// Import required modules
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Check if environment variables are set
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase URL and/or Anon Key are not set in environment variables.');
  console.error('Please make sure to set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Path to the JSON file
const jsonFilePath = path.join(process.cwd(), 'public', 'data', 'existencia.json');

// Function to read and parse the JSON file
async function readJsonFile(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error('Error reading JSON file:', error);
    process.exit(1);
  }
}

// Function to transform product data to match the database schema
function transformProductData(products) {
  return products.map(product => ({
    id: product.id,
    name: product.name,
    description: product.description,
    long_description: product.longDescription || '',
    price: product.price,
    availability: product.availability,
    stock: product.stock,
    reorder_point: product.reorderPoint || 0,
    size: product.size || '',
    image_src: product.imageSrc,
    image_alt: product.imageAlt,
    image_hint: product.imageHint || '',
    tags: product.tags || [],
    gallery: product.gallery || [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));
}

// Function to insert products into Supabase
async function insertProducts(products) {
  try {
    const { data, error } = await supabase
      .from('products')
      .insert(products)
      .select();

    if (error) {
      console.error('Error inserting products:', error);
      return false;
    }

    console.log(`Successfully inserted ${data.length} products`);
    return true;
  } catch (error) {
    console.error('Error in insertProducts:', error);
    return false;
  }
}

// Main function to run the import
async function main() {
  console.log('Starting product import...');
  
  // Read and parse the JSON file
  const products = await readJsonFile(jsonFilePath);
  console.log(`Found ${products.length} products in the JSON file`);
  
  // Transform the product data
  const transformedProducts = transformProductData(products);
  
  // Insert products in batches to avoid hitting Supabase limits
  const batchSize = 10; // Adjust batch size as needed
  let insertedCount = 0;
  
  for (let i = 0; i < transformedProducts.length; i += batchSize) {
    const batch = transformedProducts.slice(i, i + batchSize);
    console.log(`Inserting batch ${i / batchSize + 1} of ${Math.ceil(transformedProducts.length / batchSize)}`);
    
    const success = await insertProducts(batch);
    if (!success) {
      console.error(`Failed to insert batch starting at index ${i}`);
      break;
    }
    
    insertedCount += batch.length;
    console.log(`Successfully inserted ${insertedCount}/${transformedProducts.length} products`);
    
    // Add a small delay between batches to avoid rate limiting
    if (i + batchSize < transformedProducts.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log('Product import completed');
}

// Run the main function
main().catch(console.error);
