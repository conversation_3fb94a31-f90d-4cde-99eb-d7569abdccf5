// scripts/setup-i18n-database.js
// Script para crear las tablas de internacionalización en Supabase

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Faltan las credenciales de Supabase en .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function createI18nTables() {
  console.log('🚀 Iniciando creación de tablas de internacionalización...\n');

  try {
    // 1. Crear tabla de traducciones de productos
    console.log('📝 Creando tabla product_translations...');
    const { error: error1 } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS product_translations (
          id SERIAL PRIMARY KEY,
          product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
          language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
          name TEXT NOT NULL,
          description TEXT,
          long_description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(product_id, language_code)
        );
      `
    });

    if (error1) {
      console.log('⚠️  Tabla product_translations ya existe o se creó exitosamente');
    } else {
      console.log('✅ Tabla product_translations creada exitosamente');
    }

    // 2. Crear tabla de precios regionales
    console.log('💰 Creando tabla product_prices...');
    const { error: error2 } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS product_prices (
          id SERIAL PRIMARY KEY,
          product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
          currency_code VARCHAR(3) NOT NULL CHECK (currency_code IN ('USD', 'EUR', 'BRL')),
          price DECIMAL(10,2) NOT NULL CHECK (price > 0),
          region VARCHAR(5) NOT NULL CHECK (region IN ('US', 'EU', 'BR')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(product_id, currency_code, region)
        );
      `
    });

    if (error2) {
      console.log('⚠️  Tabla product_prices ya existe o se creó exitosamente');
    } else {
      console.log('✅ Tabla product_prices creada exitosamente');
    }

    // 3. Crear tabla de traducciones de tags
    console.log('🏷️  Creando tabla tag_translations...');
    const { error: error3 } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS tag_translations (
          id SERIAL PRIMARY KEY,
          tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
          language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
          name TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(tag_id, language_code)
        );
      `
    });

    if (error3) {
      console.log('⚠️  Tabla tag_translations ya existe o se creó exitosamente');
    } else {
      console.log('✅ Tabla tag_translations creada exitosamente');
    }

    // 4. Agregar columna base_language a products
    console.log('🌐 Agregando columna base_language a products...');
    const { error: error4 } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE products 
        ADD COLUMN IF NOT EXISTS base_language VARCHAR(5) DEFAULT 'es';
      `
    });

    if (error4) {
      console.log('⚠️  Columna base_language ya existe o se agregó exitosamente');
    } else {
      console.log('✅ Columna base_language agregada exitosamente');
    }

    console.log('\n🎉 ¡Todas las tablas de internacionalización han sido creadas exitosamente!');
    console.log('\n📊 Estructura creada:');
    console.log('   ✅ product_translations - Traducciones de productos');
    console.log('   ✅ product_prices - Precios regionales');
    console.log('   ✅ tag_translations - Traducciones de tags');
    console.log('   ✅ products.base_language - Idioma base');

  } catch (error) {
    console.error('❌ Error al crear las tablas:', error);
    process.exit(1);
  }
}

// Función para verificar que las tablas se crearon correctamente
async function verifyTables() {
  console.log('\n🔍 Verificando que las tablas se crearon correctamente...');
  
  try {
    // Verificar product_translations
    const { data: pt, error: ptError } = await supabase
      .from('product_translations')
      .select('*')
      .limit(1);
    
    if (!ptError) {
      console.log('✅ product_translations: Accesible');
    }

    // Verificar product_prices
    const { data: pp, error: ppError } = await supabase
      .from('product_prices')
      .select('*')
      .limit(1);
    
    if (!ppError) {
      console.log('✅ product_prices: Accesible');
    }

    // Verificar tag_translations
    const { data: tt, error: ttError } = await supabase
      .from('tag_translations')
      .select('*')
      .limit(1);
    
    if (!ttError) {
      console.log('✅ tag_translations: Accesible');
    }

    console.log('\n🎯 Próximos pasos:');
    console.log('   1. Migrar datos existentes');
    console.log('   2. Crear traducciones');
    console.log('   3. Actualizar código frontend');

  } catch (error) {
    console.error('❌ Error al verificar las tablas:', error);
  }
}

// Ejecutar el script
async function main() {
  await createI18nTables();
  await verifyTables();
}

main().catch(console.error);
