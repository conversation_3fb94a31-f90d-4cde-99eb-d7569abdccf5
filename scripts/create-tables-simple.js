// scripts/create-tables-simple.js
// Script simplificado para crear tablas de internacionalización

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔧 Configuración:');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey ? 'Configurada' : 'No configurada');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Faltan las credenciales de Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('\n🔍 Probando conexión a Supabase...');
  
  try {
    const { data, error } = await supabase
      .from('products')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('❌ Error de conexión:', error);
      return false;
    }
    
    console.log('✅ Conexión exitosa');
    return true;
  } catch (error) {
    console.error('❌ Error de conexión:', error);
    return false;
  }
}

async function createTables() {
  console.log('\n🚀 Creando tablas de internacionalización...');
  
  // Intentar crear las tablas usando INSERT directo para probar permisos
  try {
    // Verificar si podemos acceder a las tablas existentes
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name')
      .limit(1);
    
    if (productsError) {
      console.error('❌ No se puede acceder a la tabla products:', productsError);
      return;
    }
    
    console.log('✅ Acceso a tabla products confirmado');
    console.log('📊 Producto de ejemplo:', products[0]);
    
    // Verificar si podemos acceder a tags
    const { data: tags, error: tagsError } = await supabase
      .from('tags')
      .select('id, name')
      .limit(1);
    
    if (tagsError) {
      console.error('❌ No se puede acceder a la tabla tags:', tagsError);
      return;
    }
    
    console.log('✅ Acceso a tabla tags confirmado');
    console.log('🏷️  Tag de ejemplo:', tags[0]);
    
    console.log('\n⚠️  NOTA IMPORTANTE:');
    console.log('Las tablas de internacionalización deben crearse desde el panel de Supabase');
    console.log('o usando una clave de servicio con permisos de administrador.');
    console.log('\nPor favor, ejecuta el siguiente SQL en el panel de Supabase:');
    
    console.log('\n📝 SQL PARA EJECUTAR EN SUPABASE:');
    console.log('=====================================');
    
    const sql = `
-- 1. Tabla de traducciones de productos
CREATE TABLE IF NOT EXISTS product_translations (
  id SERIAL PRIMARY KEY,
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  description TEXT,
  long_description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, language_code)
);

-- 2. Tabla de precios regionales
CREATE TABLE IF NOT EXISTS product_prices (
  id SERIAL PRIMARY KEY,
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  currency_code VARCHAR(3) NOT NULL CHECK (currency_code IN ('USD', 'EUR', 'BRL')),
  price DECIMAL(10,2) NOT NULL CHECK (price > 0),
  region VARCHAR(5) NOT NULL CHECK (region IN ('US', 'EU', 'BR')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, currency_code, region)
);

-- 3. Tabla de traducciones de tags
CREATE TABLE IF NOT EXISTS tag_translations (
  id SERIAL PRIMARY KEY,
  tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tag_id, language_code)
);

-- 4. Agregar columna base_language
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS base_language VARCHAR(5) DEFAULT 'es';

-- 5. Crear índices
CREATE INDEX IF NOT EXISTS idx_product_translations_product_lang 
ON product_translations(product_id, language_code);

CREATE INDEX IF NOT EXISTS idx_product_prices_product_currency 
ON product_prices(product_id, currency_code);

CREATE INDEX IF NOT EXISTS idx_tag_translations_tag_lang 
ON tag_translations(tag_id, language_code);
`;
    
    console.log(sql);
    console.log('=====================================');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

async function main() {
  const connected = await testConnection();
  if (connected) {
    await createTables();
  }
}

main().catch(console.error);
