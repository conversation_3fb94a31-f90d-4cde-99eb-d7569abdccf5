// scripts/run-with-credentials.js
const readline = require('readline').createInterface({
  input: process.stdin,
  output: process.stdout
});

readline.question('Por favor ingresa tu URL de Supabase: ', (url) => {
  process.env.NEXT_PUBLIC_SUPABASE_URL = url.trim();
  
  readline.question('Por favor ingresa tu clave anónima de Supabase: ', (key) => {
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = key.trim();
    
    console.log('\nIniciando el script...\n');
    
    // Importar y ejecutar el script principal
    require('./populate-product-tags');
    
    readline.close();
  });
});
