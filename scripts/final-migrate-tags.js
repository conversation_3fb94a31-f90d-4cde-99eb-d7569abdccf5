// scripts/final-migrate-tags.js
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Configuración de Supabase
const supabaseUrl = 'https://speynvlzzknzhpukhpyg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNwZXludmx6emtuemhwdWtocHlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTY3NjUyODgsImV4cCI6MjAzMjM0MTI4OH0.2mX3t6X3J8QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9QZ9';

// Inicializar cliente de Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateTags() {
  try {
    console.log('Iniciando migración de tags...');
    
    // Leer el archivo existencia.json
    const data = await fs.readFile(path.join(__dirname, '../public/data/existencia.json'), 'utf8');
    const products = JSON.parse(data);
    
    // Extraer todos los tags únicos
    const allTags = new Set();
    const productTags = [];
    
    products.forEach(product => {
      if (product.tags && Array.isArray(product.tags)) {
        product.tags.forEach(tag => {
          if (tag && typeof tag === 'string') {
            allTags.add(tag.trim());
            productTags.push({
              product_id: product.id,
              tag_name: tag.trim()
            });
          }
        });
      }
    });
    
    console.log(`Se encontraron ${allTags.size} tags únicos en ${products.length} productos`);
    
    // Obtener todos los tags existentes
    const { data: existingTags, error: fetchError } = await supabase
      .from('tags')
      .select('id, name');
    
    if (fetchError) {
      console.error('Error al obtener tags existentes:', fetchError);
      return;
    }
    
    const existingTagNames = new Set(existingTags.map(t => t.name));
    const newTags = Array.from(allTags).filter(tag => !existingTagNames.has(tag));
    
    // Insertar tags que no existen
    if (newTags.length > 0) {
      console.log(`Insertando ${newTags.length} nuevos tags...`);
      const { data: insertedTags, error: insertError } = await supabase
        .from('tags')
        .insert(newTags.map(name => ({ name })))
        .select();
      
      if (insertError) {
        console.error('Error al insertar nuevos tags:', insertError);
        return;
      }
      console.log(`Se insertaron ${insertedTags.length} nuevos tags`);
    } else {
      console.log('No hay tags nuevos para insertar');
    }
    
    // Obtener todos los tags con sus IDs
    const { data: allTagsFromDb, error: tagsError } = await supabase
      .from('tags')
      .select('id, name');
    
    if (tagsError) {
      console.error('Error al obtener tags de la base de datos:', tagsError);
      return;
    }
    
    const tagNameToId = {};
    allTagsFromDb.forEach(tag => {
      tagNameToId[tag.name] = tag.id;
    });
    
    // Preparar relaciones de product_tags
    const productTagRelations = [];
    
    // Crear relaciones
    for (const rel of productTags) {
      const tagId = tagNameToId[rel.tag_name];
      if (tagId) {
        productTagRelations.push({
          product_id: rel.product_id,
          tag_id: tagId
        });
      }
    }
    
    console.log(`Se insertarán ${productTagRelations.length} relaciones`);
    
    // Insertar relaciones en lotes de 50
    const batchSize = 50;
    let insertedCount = 0;
    
    for (let i = 0; i < productTagRelations.length; i += batchSize) {
      const batch = productTagRelations.slice(i, i + batchSize);
      console.log(`Insertando lote de ${i + 1} a ${Math.min(i + batch.length, productTagRelations.length)}...`);
      
      const { error: insertRelError } = await supabase
        .from('product_tags')
        .insert(batch);
      
      if (insertRelError) {
        console.error(`Error al insertar lote de relaciones (${i + 1} - ${i + batch.length}):`, insertRelError);
      } else {
        insertedCount += batch.length;
        console.log(`Insertadas ${insertedCount}/${productTagRelations.length} relaciones`);
      }
    }
    
    console.log('Migración completada exitosamente');
    
  } catch (error) {
    console.error('Error durante la migración:', error);
  }
}

// Ejecutar la migración
migrateTags();
