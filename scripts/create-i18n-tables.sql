-- =====================================================
-- SCRIPT DE INTERNACIONALIZACIÓN PARA LUMINARA
-- Crea tablas para soporte multiidioma y precios regionales
-- =====================================================

-- 1. TABLA DE TRADUCCIONES DE PRODUCTOS
-- Almacena traducciones de nombres y descripciones
CREATE TABLE IF NOT EXISTS product_translations (
  id SERIAL PRIMARY KEY,
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  description TEXT,
  long_description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraint para evitar duplicados
  UNIQUE(product_id, language_code)
);

-- 2. TABLA DE PRECIOS REGIONALES
-- Almacena precios en diferentes monedas por región
CREATE TABLE IF NOT EXISTS product_prices (
  id SERIAL PRIMARY KEY,
  product_id TEXT NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  currency_code VARCHAR(3) NOT NULL CHECK (currency_code IN ('USD', 'EUR', 'BRL')),
  price DECIMAL(10,2) NOT NULL CHECK (price > 0),
  region VARCHAR(5) NOT NULL CHECK (region IN ('US', 'EU', 'BR')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraint para evitar duplicados
  UNIQUE(product_id, currency_code, region)
);

-- 3. TABLA DE TRADUCCIONES DE TAGS
-- Almacena traducciones de los tags/etiquetas
CREATE TABLE IF NOT EXISTS tag_translations (
  id SERIAL PRIMARY KEY,
  tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
  language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'pt', 'fr', 'de')),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraint para evitar duplicados
  UNIQUE(tag_id, language_code)
);

-- 4. AGREGAR COLUMNA DE IDIOMA BASE A PRODUCTOS
-- Marca el español como idioma base
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS base_language VARCHAR(5) DEFAULT 'es';

-- 5. CREAR ÍNDICES PARA OPTIMIZAR PERFORMANCE
-- Índice para consultas de traducciones de productos
CREATE INDEX IF NOT EXISTS idx_product_translations_product_lang 
ON product_translations(product_id, language_code);

-- Índice para consultas de precios
CREATE INDEX IF NOT EXISTS idx_product_prices_product_currency 
ON product_prices(product_id, currency_code);

-- Índice para consultas de traducciones de tags
CREATE INDEX IF NOT EXISTS idx_tag_translations_tag_lang 
ON tag_translations(tag_id, language_code);

-- Índice para consultas por región
CREATE INDEX IF NOT EXISTS idx_product_prices_region 
ON product_prices(region, currency_code);

-- 6. CREAR FUNCIÓN PARA ACTUALIZAR TIMESTAMPS
-- Función para actualizar automáticamente updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 7. CREAR TRIGGERS PARA AUTO-UPDATE DE TIMESTAMPS
-- Trigger para product_translations
DROP TRIGGER IF EXISTS update_product_translations_updated_at ON product_translations;
CREATE TRIGGER update_product_translations_updated_at
    BEFORE UPDATE ON product_translations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para product_prices
DROP TRIGGER IF EXISTS update_product_prices_updated_at ON product_prices;
CREATE TRIGGER update_product_prices_updated_at
    BEFORE UPDATE ON product_prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger para tag_translations
DROP TRIGGER IF EXISTS update_tag_translations_updated_at ON tag_translations;
CREATE TRIGGER update_tag_translations_updated_at
    BEFORE UPDATE ON tag_translations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. COMENTARIOS PARA DOCUMENTACIÓN
COMMENT ON TABLE product_translations IS 'Traducciones de productos en diferentes idiomas';
COMMENT ON TABLE product_prices IS 'Precios de productos en diferentes monedas y regiones';
COMMENT ON TABLE tag_translations IS 'Traducciones de tags/etiquetas en diferentes idiomas';

COMMENT ON COLUMN product_translations.language_code IS 'Código de idioma: en, pt, fr, de';
COMMENT ON COLUMN product_prices.currency_code IS 'Código de moneda: USD, EUR, BRL';
COMMENT ON COLUMN product_prices.region IS 'Código de región: US, EU, BR';

-- =====================================================
-- SCRIPT COMPLETADO
-- Las tablas de internacionalización han sido creadas
-- =====================================================
