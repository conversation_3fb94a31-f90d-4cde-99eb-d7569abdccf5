// Script para importar todos los productos desde existencia.json a Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Cargar variables de entorno
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Verificar credenciales
if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Faltan las credenciales de Supabase en el archivo .env.local');
  console.log('Asegúrate de tener configuradas las siguientes variables:');
  console.log('NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=tu_clave_anonima');
  process.exit(1);
}

// Inicializar cliente de Supabase
const supabase = createClient(supabaseUrl, supabaseKey);

// Ruta al archivo JSON
const jsonFilePath = path.join(process.cwd(), 'public', 'data', 'existencia.json');

// Función para leer el archivo JSON
function readJsonFile(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error('Error al leer el archivo JSON:', error);
    process.exit(1);
  }
}

// Función para transformar los datos del producto al formato de la base de datos
function transformProduct(product) {
  return {
    id: product.id,
    name: product.name,
    description: product.description || '',
    long_description: product.longDescription || '',
    price: product.price || '$0.00',
    availability: product.availability !== undefined ? product.availability : true,
    stock: product.stock || 0,
    reorder_point: product.reorderPoint || 0,
    size: product.size || '',
    image_src: product.imageSrc || '',
    image_alt: product.imageAlt || '',
    image_hint: product.imageHint || '',
    tags: product.tags || [],
    gallery: product.gallery || [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
}

// Función para insertar productos en lotes
async function insertProducts(products) {
  const batchSize = 10; // Tamaño del lote para evitar sobrecargar la API
  let insertedCount = 0;
  const errors = [];

  for (let i = 0; i < products.length; i += batchSize) {
    const batch = products.slice(i, i + batchSize);
    console.log(`Insertando lote ${Math.floor(i / batchSize) + 1} de ${Math.ceil(products.length / batchSize)}...`);
    
    try {
      const { data, error } = await supabase
        .from('products')
        .upsert(batch, { onConflict: 'id' })
        .select();

      if (error) {
        console.error(`Error al insertar lote ${i / batchSize + 1}:`, error);
        errors.push(...batch.map(p => ({ id: p.id, error: error.message })));
      } else {
        insertedCount += data.length;
        console.log(`Lote ${Math.floor(i / batchSize) + 1} insertado: ${data.length} productos`);
      }
    } catch (error) {
      console.error(`Error inesperado al insertar lote ${i / batchSize + 1}:`, error);
      errors.push(...batch.map(p => ({ id: p.id, error: error.message })));
    }

    // Pequeña pausa entre lotes para evitar sobrecargar la API
    if (i + batchSize < products.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return { insertedCount, errors };
}

// Función principal
async function main() {
  console.log('=== Iniciando importación de productos ===');
  
  // Leer el archivo JSON
  console.log('Leyendo archivo JSON...');
  const products = readJsonFile(jsonFilePath);
  console.log(`Se encontraron ${products.length} productos en el archivo.`);
  
  // Transformar los productos
  console.log('Transformando datos...');
  const transformedProducts = products.map(transformProduct);
  
  // Insertar productos en la base de datos
  console.log('Insertando productos en la base de datos...');
  const { insertedCount, errors } = await insertProducts(transformedProducts);
  
  // Mostrar resumen
  console.log('\n=== Resumen de la importación ===');
  console.log(`Total de productos procesados: ${products.length}`);
  console.log(`Productos insertados/actualizados: ${insertedCount}`);
  console.log(`Errores: ${errors.length}`);
  
  if (errors.length > 0) {
    console.log('\n=== Productos con errores ===');
    console.table(errors);
    console.log('\nRevisa los errores y considera reintentar la importación.');
  }
  
  console.log('\n¡Importación completada!');
}

// Ejecutar el script
main().catch(error => {
  console.error('Error durante la importación:', error);
  process.exit(1);
});
