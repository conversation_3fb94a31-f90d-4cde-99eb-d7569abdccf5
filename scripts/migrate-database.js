// scripts/migrate-database.js
// Script para migrar la base de datos y crear tablas de internacionalización

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: Faltan las credenciales de Supabase en .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateDatabase() {
  console.log('🚀 Iniciando migración de base de datos para internacionalización...\n');

  try {
    // Paso 1: Migrar precios existentes a tabla product_prices
    console.log('💰 Paso 1: Migrando precios existentes...');
    
    // Obtener todos los productos con sus precios actuales
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, price');
    
    if (productsError) {
      console.error('❌ Error al obtener productos:', productsError);
      return;
    }
    
    console.log(`📊 Encontrados ${products.length} productos para migrar`);
    
    // Crear registros de precios en MXN para cada producto
    const priceRecords = products.map(product => {
      // Extraer el precio numérico del string (ej: "$250.00" -> 250.00)
      const numericPrice = parseFloat(product.price.replace(/[$,]/g, ''));
      
      return {
        product_id: product.id,
        currency_code: 'MXN',
        price: numericPrice,
        region: 'MX'
      };
    });
    
    // Insertar precios base en MXN
    const { error: pricesError } = await supabase
      .from('product_prices')
      .insert(priceRecords);
    
    if (pricesError) {
      console.log('⚠️  Los precios ya fueron migrados o hay un error:', pricesError.message);
    } else {
      console.log('✅ Precios migrados exitosamente a product_prices');
    }
    
    // Paso 2: Crear precios en otras monedas
    console.log('\n💱 Paso 2: Creando precios en otras monedas...');
    
    // Tasas de cambio aproximadas (MXN base)
    const exchangeRates = {
      'USD': 0.055,  // 1 MXN = 0.055 USD
      'EUR': 0.051,  // 1 MXN = 0.051 EUR  
      'BRL': 0.32    // 1 MXN = 0.32 BRL
    };
    
    const regions = {
      'USD': 'US',
      'EUR': 'EU', 
      'BRL': 'BR'
    };
    
    for (const [currency, rate] of Object.entries(exchangeRates)) {
      const convertedPrices = products.map(product => {
        const mxnPrice = parseFloat(product.price.replace(/[$,]/g, ''));
        const convertedPrice = Math.round(mxnPrice * rate * 100) / 100; // Redondear a 2 decimales
        
        return {
          product_id: product.id,
          currency_code: currency,
          price: convertedPrice,
          region: regions[currency]
        };
      });
      
      const { error: currencyError } = await supabase
        .from('product_prices')
        .insert(convertedPrices);
      
      if (currencyError) {
        console.log(`⚠️  Precios en ${currency} ya existen o hay un error:`, currencyError.message);
      } else {
        console.log(`✅ Precios en ${currency} creados exitosamente`);
      }
    }
    
    // Paso 3: Verificar migración
    console.log('\n🔍 Paso 3: Verificando migración...');
    
    const { data: priceCheck, error: priceCheckError } = await supabase
      .from('product_prices')
      .select('currency_code, count(*)')
      .group('currency_code');
    
    if (!priceCheckError && priceCheck) {
      console.log('📊 Resumen de precios por moneda:');
      priceCheck.forEach(item => {
        console.log(`   ${item.currency_code}: ${item.count} productos`);
      });
    }
    
    console.log('\n🎉 ¡Migración de precios completada exitosamente!');
    
  } catch (error) {
    console.error('❌ Error durante la migración:', error);
  }
}

async function createSampleTranslations() {
  console.log('\n🌍 Creando traducciones de ejemplo...');
  
  try {
    // Obtener algunos productos para crear traducciones de ejemplo
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, description, long_description')
      .limit(3);
    
    if (productsError) {
      console.error('❌ Error al obtener productos:', productsError);
      return;
    }
    
    // Traducciones de ejemplo para el primer producto
    const sampleTranslations = [
      {
        product_id: products[0].id,
        language_code: 'en',
        name: 'Relaxing Lavender Candle',
        description: 'Calm and serenity with pure lavender essential oil.',
        long_description: 'Our Relaxing Lavender Candle is made with natural soy wax and 100% pure lavender essential oil. Its soft and floral aroma is perfect for creating an atmosphere of calm and serenity, ideal for relaxing after a long day or to accompany your meditation moments. Approximate duration: 40 hours.'
      },
      {
        product_id: products[0].id,
        language_code: 'pt',
        name: 'Vela Relaxante de Lavanda',
        description: 'Calma e serenidade com óleo essencial de lavanda pura.',
        long_description: 'Nossa Vela Relaxante de Lavanda é feita com cera de soja natural e óleo essencial de lavanda 100% puro. Seu aroma suave e floral é perfeito para criar uma atmosfera de calma e serenidade, ideal para relaxar após um longo dia ou para acompanhar seus momentos de meditação. Duração aproximada: 40 horas.'
      },
      {
        product_id: products[0].id,
        language_code: 'fr',
        name: 'Bougie Relaxante à la Lavande',
        description: 'Calme et sérénité avec huile essentielle de lavande pure.',
        long_description: 'Notre Bougie Relaxante à la Lavande est fabriquée avec de la cire de soja naturelle et de l\'huile essentielle de lavande 100% pure. Son arôme doux et floral est parfait pour créer une atmosphère de calme et de sérénité, idéal pour se détendre après une longue journée ou pour accompagner vos moments de méditation. Durée approximative: 40 heures.'
      },
      {
        product_id: products[0].id,
        language_code: 'de',
        name: 'Entspannende Lavendel-Kerze',
        description: 'Ruhe und Gelassenheit mit reinem Lavendel-Ätherischöl.',
        long_description: 'Unsere Entspannende Lavendel-Kerze wird aus natürlichem Sojawachs und 100% reinem Lavendel-Ätherischöl hergestellt. Ihr sanfter und blumiger Duft ist perfekt, um eine Atmosphäre der Ruhe und Gelassenheit zu schaffen, ideal zum Entspannen nach einem langen Tag oder um Ihre Meditationsmomente zu begleiten. Ungefähre Brenndauer: 40 Stunden.'
      }
    ];
    
    const { error: translationsError } = await supabase
      .from('product_translations')
      .insert(sampleTranslations);
    
    if (translationsError) {
      console.log('⚠️  Las traducciones ya existen o hay un error:', translationsError.message);
    } else {
      console.log('✅ Traducciones de ejemplo creadas exitosamente');
    }
    
  } catch (error) {
    console.error('❌ Error al crear traducciones:', error);
  }
}

async function createTagTranslations() {
  console.log('\n🏷️  Creando traducciones de tags...');
  
  try {
    // Obtener algunos tags para traducir
    const { data: tags, error: tagsError } = await supabase
      .from('tags')
      .select('id, name')
      .limit(10);
    
    if (tagsError) {
      console.error('❌ Error al obtener tags:', tagsError);
      return;
    }
    
    // Traducciones de ejemplo para tags comunes
    const tagTranslations = {
      'Relajante': { en: 'Relaxing', pt: 'Relaxante', fr: 'Relaxant', de: 'Entspannend' },
      'Floral': { en: 'Floral', pt: 'Floral', fr: 'Floral', de: 'Blumig' },
      'Aromaterapia': { en: 'Aromatherapy', pt: 'Aromaterapia', fr: 'Aromathérapie', de: 'Aromatherapie' },
      'Energizante': { en: 'Energizing', pt: 'Energizante', fr: 'Énergisant', de: 'Energetisierend' },
      'Cítrico': { en: 'Citrus', pt: 'Cítrico', fr: 'Agrumes', de: 'Zitrus' },
      'Fresco': { en: 'Fresh', pt: 'Fresco', fr: 'Frais', de: 'Frisch' },
      'Dulce': { en: 'Sweet', pt: 'Doce', fr: 'Doux', de: 'Süß' },
      'Reconfortante': { en: 'Comforting', pt: 'Reconfortante', fr: 'Réconfortant', de: 'Tröstend' },
      'Gourmet': { en: 'Gourmet', pt: 'Gourmet', fr: 'Gourmet', de: 'Gourmet' },
      'Natural': { en: 'Natural', pt: 'Natural', fr: 'Naturel', de: 'Natürlich' }
    };
    
    const translationsToInsert = [];
    
    tags.forEach(tag => {
      if (tagTranslations[tag.name]) {
        const translations = tagTranslations[tag.name];
        Object.entries(translations).forEach(([lang, translation]) => {
          translationsToInsert.push({
            tag_id: tag.id,
            language_code: lang,
            name: translation
          });
        });
      }
    });
    
    if (translationsToInsert.length > 0) {
      const { error: tagTransError } = await supabase
        .from('tag_translations')
        .insert(translationsToInsert);
      
      if (tagTransError) {
        console.log('⚠️  Las traducciones de tags ya existen o hay un error:', tagTransError.message);
      } else {
        console.log(`✅ ${translationsToInsert.length} traducciones de tags creadas exitosamente`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error al crear traducciones de tags:', error);
  }
}

async function main() {
  console.log('🌍 MIGRACIÓN DE INTERNACIONALIZACIÓN PARA LUMINARA');
  console.log('================================================\n');
  
  await migrateDatabase();
  await createSampleTranslations();
  await createTagTranslations();
  
  console.log('\n🎉 ¡Migración completada exitosamente!');
  console.log('\n📋 Próximos pasos:');
  console.log('   1. Verificar datos en Supabase');
  console.log('   2. Crear funciones de consulta multiidioma');
  console.log('   3. Actualizar componentes frontend');
}

main().catch(console.error);
