-- <PERSON><PERSON>, verificar si la tabla existe, si no, crearla
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'products')
BEGIN
    CREATE TABLE products (
        id NVARCHAR(100) PRIMARY KEY,
        name NVARCHAR(255) NOT NULL,
        description NVARCHAR(MAX),
        long_description NVARCHAR(MAX),
        price DECIMAL(10,2) NOT NULL,
        availability BIT DEFAULT 1,
        stock INT NOT NULL,
        reorder_point INT,
        size NVARCHAR(50),
        image_src NVARCHAR(1000),
        image_alt NVARCHAR(255),
        image_hint NVARCHAR(255),
        tags NVARCHAR(MAX), -- Almacenaremos JSON para los tags
        gallery NVARCHAR(MAX), -- Almacenaremos JSON para la galería
        created_at DATETIMEOFFSET DEFAULT SYSDATETIMEOFFSET()
    );
    PRINT 'Tabla products creada exitosamente';
END
ELSE
BEGIN
    PRINT 'La tabla products ya existe';
END
GO

-- Insertar productos de ejemplo en la tabla products
-- Usamos MERGE para manejar la inserción o actualización
MERGE INTO products AS target
USING (VALUES
    (
        'vela-aromatica-lavanda',
        'Vela Relajante de Lavanda',
        'Calma y serenidad con aceite esencial de lavanda pura.',
        'Nuestra Vela Relajante de Lavanda está elaborada con cera de soya natural y aceite esencial de lavanda 100% puro. Su aroma suave y floral es perfecto para crear un ambiente de calma y serenidad, ideal para relajarse después de un largo día o para acompañar tus momentos de meditación. Duración aproximada: 40 horas.',
        250.00,
        1,
        15,
        5,
        'Mediana',
        'https://placehold.co/400x300.png',
        'Vela aromática de lavanda',
        'lavender candle',
        '["Relajante", "Floral", "Aromaterapia"]',
        '["https://placehold.co/600x600.png", "https://placehold.co/600x600.png", "https://placehold.co/600x600.png"]'
    ),
    (
        'vela-citrica-energia',
        'Vela Energizante Cítrica',
        'Frescura vibrante de naranja y limón para revitalizar.',
        'Despierta tus sentidos con la Vela Energizante Cítrica. Una explosión de frescura con aceites esenciales de naranja dulce y limón, diseñada para revitalizar tu espacio y tu ánimo. Perfecta para las mañanas o cuando necesitas un impulso de energía. Duración aproximada: 35 horas.',
        220.00,
        1,
        20,
        8,
        'Mediana',
        'https://placehold.co/400x300.png',
        'Vela cítrica energizante',
        'citrus candle',
        '["Energizante", "Cítrico", "Fresco"]',
        '["https://placehold.co/600x600.png", "https://placehold.co/600x600.png"]'
    ),
    (
        'vela-vainilla-calida',
        'Vela Cálida de Vainilla',
        'Dulce y reconfortante aroma de vainilla de Madagascar.',
        'Envuelve tu hogar en la dulzura reconfortante de nuestra Vela Cálida de Vainilla. Hecha con extracto puro de vainilla de Madagascar, su aroma es rico y acogedor, creando una atmósfera perfecta para momentos de relax y confort. Duración aproximada: 45 horas.',
        280.00,
        1,
        10,
        3,
        'Grande',
        'https://placehold.co/400x300.png',
        'Vela de vainilla cálida',
        'vanilla candle',
        '["Dulce", "Reconfortante", "Gourmet"]',
        '["https://placehold.co/600x600.png"]'
    )
) AS source (
    id, name, description, long_description, price, availability, 
    stock, reorder_point, size, image_src, image_alt, image_hint, tags, gallery
)
ON target.id = source.id
WHEN MATCHED THEN
    UPDATE SET 
        name = source.name,
        description = source.description,
        long_description = source.long_description,
        price = source.price,
        availability = source.availability,
        stock = source.stock,
        reorder_point = source.reorder_point,
        size = source.size,
        image_src = source.image_src,
        image_alt = source.image_alt,
        image_hint = source.image_hint,
        tags = source.tags,
        gallery = source.gallery
WHEN NOT MATCHED THEN
    INSERT (
        id, name, description, long_description, price, availability, 
        stock, reorder_point, size, image_src, image_alt, image_hint, tags, gallery
    )
    VALUES (
        source.id, source.name, source.description, source.long_description, 
        source.price, source.availability, source.stock, source.reorder_point, 
        source.size, source.image_src, source.image_alt, source.image_hint, 
        source.tags, source.gallery
    );

-- Verificar que los datos se insertaron correctamente
SELECT id, name, price, stock, created_at 
FROM products 
ORDER BY created_at DESC;
