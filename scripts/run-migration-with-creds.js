// Script temporal para ejecutar la migración con credenciales proporcionadas
process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://speynvlzzknzhpukhpyg.supabase.co';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNwZXludmx6emtuemhwdWtocHlnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ2MDYsImV4cCI6MjA2MzkzMDYwNn0.0B0qTmRkG8fShvYIJKIADtDksivpXb_2aYiVoJxyh0Q';

console.log('Iniciando migración de tags con las credenciales proporcionadas...');
console.log('URL de Supabase:', process.env.NEXT_PUBLIC_SUPABASE_URL);

// Importar y ejecutar el script de migración
require('./populate-product-tags');
