// scripts/populate-product-tags.js
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs').promises;
const path = require('path');

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Faltan las variables de entorno de Supabase');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    // 1. Leer el archivo existencia.json
    const filePath = path.join(process.cwd(), 'public/data/existencia.json');
    const fileContent = await fs.readFile(filePath, 'utf-8');
    const products = JSON.parse(fileContent);

    console.log(`Procesando ${products.length} productos...`);

    // 2. Extraer todos los tags únicos
    const allTags = new Set();
    const productTagsMap = new Map(); // Mapa de product_id a array de tags

    products.forEach(product => {
      if (product.tags && Array.isArray(product.tags)) {
        // Guardar los tags del producto en el mapa
        productTagsMap.set(product.id, product.tags);
        
        // Agregar tags al conjunto de tags únicos
        product.tags.forEach(tag => allTags.add(tag.trim()));
      }
    });

    console.log(`Encontrados ${allTags.size} tags únicos`);
    console.log(`Encontradas relaciones para ${productTagsMap.size} productos`);

    // 3. Insertar tags en la tabla 'tags' (si no existen)
    const tagsArray = Array.from(allTags);
    const { data: existingTags, error: fetchTagsError } = await supabase
      .from('tags')
      .select('name');

    if (fetchTagsError) {
      throw new Error(`Error al obtener tags existentes: ${fetchTagsError.message}`);
    }

    const existingTagNames = new Set(existingTags.map(tag => tag.name));
    const newTags = tagsArray.filter(tag => !existingTagNames.has(tag));

    if (newTags.length > 0) {
      console.log(`Insertando ${newTags.length} nuevos tags...`);
      
      const { error: insertTagsError } = await supabase
        .from('tags')
        .insert(newTags.map(name => ({ name })));
      
      if (insertTagsError) {
        throw new Error(`Error al insertar tags: ${insertTagsError.message}`);
      }
      console.log('Tags insertados correctamente');
    } else {
      console.log('No hay tags nuevos para insertar');
    }

    // 4. Obtener todos los tags con sus IDs
    const { data: allTagsFromDb, error: tagsError } = await supabase
      .from('tags')
      .select('id, name');

    if (tagsError) {
      throw new Error(`Error al obtener tags de la base de datos: ${tagsError.message}`);
    }

    const tagNameToId = new Map(allTagsFromDb.map(tag => [tag.name, tag.id]));

    // 5. Insertar relaciones en la tabla product_tags
    console.log('Insertando relaciones en product_tags...');
    
    // Primero, obtener los IDs de productos existentes
    const { data: existingProducts, error: productsError } = await supabase
      .from('products')
      .select('id');

    if (productsError) {
      throw new Error(`Error al obtener productos existentes: ${productsError.message}`);
    }

    const existingProductIds = new Set(existingProducts.map(p => p.id));
    
    // Obtener las relaciones existentes para evitar duplicados
    const { data: existingRelations, error: relationsError } = await supabase
      .from('product_tags')
      .select('product_id, tag_id');

    if (relationsError) {
      throw new Error(`Error al obtener relaciones existentes: ${relationsError.message}`);
    }

    const existingRelationsSet = new Set(
      existingRelations.map(rel => `${rel.product_id}-${rel.tag_id}`)
    );

    // Preparar las relaciones para insertar
    const relationsToInsert = [];
    
    for (const [productId, tags] of productTagsMap.entries()) {
      // Verificar que el producto exista en la base de datos
      if (!existingProductIds.has(productId)) {
        console.log(`El producto con ID ${productId} no existe en la base de datos, omitiendo...`);
        continue;
      }
      
      for (const tagName of tags) {
        const tagId = tagNameToId.get(tagName);
        if (tagId && !existingRelationsSet.has(`${productId}-${tagId}`)) {
          relationsToInsert.push({
            product_id: productId,
            tag_id: tagId,
            created_at: new Date().toISOString()
          });
        }
      }
    }

    // Insertar las relaciones en lotes
    const BATCH_SIZE = 100;
    let insertedCount = 0;

    for (let i = 0; i < relationsToInsert.length; i += BATCH_SIZE) {
      const batch = relationsToInsert.slice(i, i + BATCH_SIZE);
      const { error: insertError } = await supabase
        .from('product_tags')
        .insert(batch);
      
      if (insertError) {
        console.error(`Error al insertar lote de relaciones: ${insertError.message}`);
        continue;
      }
      
      insertedCount += batch.length;
      console.log(`Insertado lote de ${batch.length} relaciones (${insertedCount}/${relationsToInsert.length})`);
    }

    console.log('Proceso completado exitosamente');
    console.log(`Total de relaciones insertadas: ${insertedCount}`);

  } catch (error) {
    console.error('Error en el script:', error);
    process.exit(1);
  }
}

// Ejecutar el script
main().then(() => process.exit(0));
